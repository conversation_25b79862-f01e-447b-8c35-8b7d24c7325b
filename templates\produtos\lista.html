{% extends "base.html" %}

{% block title %}Produtos - Saúde Flex{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-box me-2 text-primary"></i>
                Produtos
            </h1>
            <a href="{{ url_for('produto_novo') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                Novo Produto
            </a>
        </div>
    </div>
</div>

<!-- Filtros -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="filtro-nome">Nome do Produto</label>
                            <input type="text" class="form-control" id="filtro-nome" placeholder="Buscar por nome...">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="filtro-categoria">Categoria</label>
                            <select class="form-control" id="filtro-categoria">
                                <option value="">Todas as categorias</option>
                                {% for categoria in categorias %}
                                <option value="{{ categoria.id }}">{{ categoria.nome }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="filtro-estoque">Estoque</label>
                            <select class="form-control" id="filtro-estoque">
                                <option value="">Todos</option>
                                <option value="baixo">Estoque Baixo</option>
                                <option value="normal">Estoque Normal</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label>&nbsp;</label>
                            <button type="button" class="btn btn-outline-secondary w-100" onclick="limparFiltros()">
                                <i class="fas fa-eraser me-1"></i>
                                Limpar
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Lista de Produtos -->
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-list me-2"></i>
                    Lista de Produtos ({{ produtos|length }})
                </h6>
            </div>
            <div class="card-body">
                {% if produtos %}
                <div class="table-responsive">
                    <table class="table table-hover" id="tabela-produtos">
                        <thead>
                            <tr>
                                <th>Nome</th>
                                <th>Categoria</th>
                                <th>Preço Custo</th>
                                <th>Preço Venda</th>
                                <th>Estoque</th>
                                <th>Status</th>
                                <th>Ações</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for produto in produtos %}
                            <tr data-categoria="{{ produto.categoria_id or '' }}" 
                                data-estoque="{{ 'baixo' if produto.estoque_atual <= produto.estoque_minimo else 'normal' }}">
                                <td>
                                    <div class="d-flex align-items-center">
                                        {% if produto.foto %}
                                        <img src="{{ url_for('static', filename='img/produtos/' + produto.foto) }}" 
                                             alt="{{ produto.nome }}" class="rounded me-2" style="width: 40px; height: 40px; object-fit: cover;">
                                        {% else %}
                                        <div class="bg-light rounded me-2 d-flex align-items-center justify-content-center" 
                                             style="width: 40px; height: 40px;">
                                            <i class="fas fa-box text-muted"></i>
                                        </div>
                                        {% endif %}
                                        <div>
                                            <strong>{{ produto.nome }}</strong>
                                            {% if produto.descricao %}
                                            <br><small class="text-muted">{{ produto.descricao[:50] }}{% if produto.descricao|length > 50 %}...{% endif %}</small>
                                            {% endif %}
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    {% if produto.categoria_nome %}
                                    <span class="badge bg-secondary">{{ produto.categoria_nome }}</span>
                                    {% else %}
                                    <span class="text-muted">Sem categoria</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="text-muted">R$</span>
                                    <strong>{{ "%.2f"|format(produto.preco_custo) }}</strong>
                                </td>
                                <td>
                                    <span class="text-success">R$</span>
                                    <strong class="text-success">{{ "%.2f"|format(produto.preco_venda) }}</strong>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <span class="me-2">{{ produto.estoque_atual }}</span>
                                        {% if produto.estoque_atual <= produto.estoque_minimo %}
                                        <span class="badge bg-warning">
                                            <i class="fas fa-exclamation-triangle me-1"></i>
                                            Baixo
                                        </span>
                                        {% else %}
                                        <span class="badge bg-success">Normal</span>
                                        {% endif %}
                                    </div>
                                    <small class="text-muted">Mín: {{ produto.estoque_minimo }}</small>
                                </td>
                                <td>
                                    {% if produto.ativo %}
                                    <span class="badge bg-success">Ativo</span>
                                    {% else %}
                                    <span class="badge bg-danger">Inativo</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-sm btn-outline-primary" 
                                                onclick="verProduto({{ produto.id }})" title="Ver Detalhes">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-warning" 
                                                onclick="editarProduto({{ produto.id }})" title="Editar">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        {% if usuario_logado.tipo in ['admin', 'gerente'] %}
                                        <button type="button" class="btn btn-sm btn-outline-danger" 
                                                onclick="excluirProduto({{ produto.id }})" title="Excluir">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">Nenhum produto cadastrado</h5>
                    <p class="text-muted">Comece cadastrando seu primeiro produto.</p>
                    <a href="{{ url_for('produto_novo') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        Cadastrar Primeiro Produto
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Filtros
document.getElementById('filtro-nome').addEventListener('input', filtrarProdutos);
document.getElementById('filtro-categoria').addEventListener('change', filtrarProdutos);
document.getElementById('filtro-estoque').addEventListener('change', filtrarProdutos);

function filtrarProdutos() {
    const nome = document.getElementById('filtro-nome').value.toLowerCase();
    const categoria = document.getElementById('filtro-categoria').value;
    const estoque = document.getElementById('filtro-estoque').value;
    
    const linhas = document.querySelectorAll('#tabela-produtos tbody tr');
    
    linhas.forEach(linha => {
        const nomeProduto = linha.querySelector('td:first-child strong').textContent.toLowerCase();
        const categoriaProduto = linha.dataset.categoria;
        const estoqueProduto = linha.dataset.estoque;
        
        let mostrar = true;
        
        if (nome && !nomeProduto.includes(nome)) {
            mostrar = false;
        }
        
        if (categoria && categoriaProduto !== categoria) {
            mostrar = false;
        }
        
        if (estoque && estoqueProduto !== estoque) {
            mostrar = false;
        }
        
        linha.style.display = mostrar ? '' : 'none';
    });
}

function limparFiltros() {
    document.getElementById('filtro-nome').value = '';
    document.getElementById('filtro-categoria').value = '';
    document.getElementById('filtro-estoque').value = '';
    filtrarProdutos();
}

function verProduto(id) {
    // Implementar modal de detalhes
    alert('Ver produto ID: ' + id);
}

function editarProduto(id) {
    // Redirecionar para edição
    window.location.href = '/produtos/editar/' + id;
}

function excluirProduto(id) {
    if (confirm('Tem certeza que deseja excluir este produto?')) {
        // Implementar exclusão
        alert('Excluir produto ID: ' + id);
    }
}
</script>
{% endblock %}
