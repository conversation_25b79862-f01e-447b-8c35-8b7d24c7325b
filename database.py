import sqlite3
import bcrypt
from datetime import datetime
import os

class Database:
    def __init__(self, db_path='saude_flex.db'):
        self.db_path = db_path
        self.init_database()
    
    def get_connection(self):
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        return conn
    
    def init_database(self):
        """Inicializa o banco de dados com todas as tabelas necessárias"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # Tabela de usuários
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS usuarios (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                nome TEXT NOT NULL,
                email TEXT UNIQUE NOT NULL,
                senha TEXT NOT NULL,
                tipo TEXT NOT NULL CHECK (tipo IN ('admin', 'gerente', 'vendedor')),
                ativo BOOLEAN DEFAULT 1,
                data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Tabela de categorias
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS categorias (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                nome TEXT NOT NULL UNIQUE,
                descricao TEXT,
                ativo BOOLEAN DEFAULT 1
            )
        ''')
        
        # Tabela de produtos
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS produtos (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                nome TEXT NOT NULL,
                descricao TEXT,
                preco_custo DECIMAL(10,2) NOT NULL,
                lucro_desejado DECIMAL(10,2) NOT NULL,
                tipo_lucro TEXT CHECK (tipo_lucro IN ('valor', 'percentual')) DEFAULT 'percentual',
                preco_venda DECIMAL(10,2) NOT NULL,
                categoria_id INTEGER,
                estoque_atual INTEGER DEFAULT 0,
                estoque_minimo INTEGER DEFAULT 5,
                foto TEXT,
                ativo BOOLEAN DEFAULT 1,
                data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (categoria_id) REFERENCES categorias (id)
            )
        ''')
        
        # Tabela de clientes
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS clientes (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                nome TEXT NOT NULL,
                telefone TEXT NOT NULL,
                email TEXT,
                logradouro TEXT,
                numero TEXT,
                bairro TEXT,
                cidade TEXT,
                uf TEXT,
                cep TEXT,
                data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Tabela de agendamentos
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS agendamentos (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                cliente_id INTEGER NOT NULL,
                produto_id INTEGER NOT NULL,
                vendedor_id INTEGER NOT NULL,
                data_agendamento DATETIME NOT NULL,
                observacoes TEXT,
                status TEXT DEFAULT 'agendado' CHECK (status IN ('agendado', 'confirmado', 'realizado', 'cancelado')),
                data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (cliente_id) REFERENCES clientes (id),
                FOREIGN KEY (produto_id) REFERENCES produtos (id),
                FOREIGN KEY (vendedor_id) REFERENCES usuarios (id)
            )
        ''')
        
        # Tabela de vendas
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS vendas (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                cliente_id INTEGER NOT NULL,
                vendedor_id INTEGER NOT NULL,
                subtotal DECIMAL(10,2) NOT NULL,
                desconto_total DECIMAL(10,2) DEFAULT 0,
                total_final DECIMAL(10,2) NOT NULL,
                data_venda TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                observacoes TEXT,
                FOREIGN KEY (cliente_id) REFERENCES clientes (id),
                FOREIGN KEY (vendedor_id) REFERENCES usuarios (id)
            )
        ''')
        
        # Tabela de itens da venda
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS itens_venda (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                venda_id INTEGER NOT NULL,
                produto_id INTEGER NOT NULL,
                quantidade INTEGER NOT NULL,
                preco_unitario DECIMAL(10,2) NOT NULL,
                desconto_item DECIMAL(10,2) DEFAULT 0,
                subtotal_item DECIMAL(10,2) NOT NULL,
                FOREIGN KEY (venda_id) REFERENCES vendas (id),
                FOREIGN KEY (produto_id) REFERENCES produtos (id)
            )
        ''')
        
        # Tabela de logs de auditoria
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS logs_auditoria (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                usuario_id INTEGER,
                acao TEXT NOT NULL,
                tabela TEXT NOT NULL,
                registro_id INTEGER,
                dados_anteriores TEXT,
                dados_novos TEXT,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (usuario_id) REFERENCES usuarios (id)
            )
        ''')
        
        conn.commit()
        
        # Criar usuário admin padrão se não existir
        self.create_default_admin()
        
        # Criar categorias padrão
        self.create_default_categories()
        
        conn.close()
    
    def create_default_admin(self):
        """Cria um usuário admin padrão"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # Verificar se já existe um admin
        cursor.execute("SELECT id FROM usuarios WHERE tipo = 'admin' LIMIT 1")
        if cursor.fetchone():
            conn.close()
            return
        
        # Criar senha hash
        senha_hash = bcrypt.hashpw('admin123'.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
        
        cursor.execute('''
            INSERT INTO usuarios (nome, email, senha, tipo)
            VALUES (?, ?, ?, ?)
        ''', ('Administrador', '<EMAIL>', senha_hash, 'admin'))
        
        conn.commit()
        conn.close()
        print("Usuário admin criado: <EMAIL> / admin123")
    
    def create_default_categories(self):
        """Cria categorias padrão"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        categorias_padrao = [
            ('Bem-estar', 'Produtos para bem-estar geral'),
            ('Saúde', 'Produtos relacionados à saúde'),
            ('Beleza', 'Produtos de beleza e cuidados pessoais'),
            ('Suplementos', 'Suplementos alimentares e vitaminas'),
            ('Equipamentos', 'Equipamentos e aparelhos de saúde')
        ]
        
        for nome, descricao in categorias_padrao:
            cursor.execute('''
                INSERT OR IGNORE INTO categorias (nome, descricao)
                VALUES (?, ?)
            ''', (nome, descricao))
        
        conn.commit()
        conn.close()

# Instância global do banco
db = Database()
