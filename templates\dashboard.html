{% extends "base.html" %}

{% block title %}Dashboard - Saúde Flex{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-tachometer-alt me-2 text-primary"></i>
                Dashboard
            </h1>
            <div class="text-muted">
                <i class="fas fa-calendar me-1"></i>
                <span id="data-atual"></span>
            </div>
        </div>
    </div>
</div>

<!-- Cards de Estatísticas -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Total de Produtos
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ estatisticas.total_produtos }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-box fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Total de Clientes
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ estatisticas.total_clientes }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Agendamentos Hoje
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ estatisticas.agendamentos_hoje }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-calendar-check fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Estoque Baixo
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ estatisticas.estoque_baixo }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Metas e Progresso -->
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-target me-2"></i>
                    Metas do Mês
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="text-sm font-weight-bold">Meta de Vendas</span>
                            <span class="text-sm text-muted">R$ 15.000 / R$ 25.000</span>
                        </div>
                        <div class="progress mb-2" style="height: 10px;">
                            <div class="progress-bar bg-success" role="progressbar" style="width: 60%" id="progresso-vendas"></div>
                        </div>
                        <small class="text-muted">60% da meta atingida</small>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="text-sm font-weight-bold">Novos Clientes</span>
                            <span class="text-sm text-muted">12 / 20</span>
                        </div>
                        <div class="progress mb-2" style="height: 10px;">
                            <div class="progress-bar bg-info" role="progressbar" style="width: 60%" id="progresso-clientes"></div>
                        </div>
                        <small class="text-muted">60% da meta atingida</small>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="text-sm font-weight-bold">Agendamentos</span>
                            <span class="text-sm text-muted">45 / 60</span>
                        </div>
                        <div class="progress mb-2" style="height: 10px;">
                            <div class="progress-bar bg-warning" role="progressbar" style="width: 75%" id="progresso-agendamentos"></div>
                        </div>
                        <small class="text-muted">75% da meta atingida</small>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="text-sm font-weight-bold">Produtos Cadastrados</span>
                            <span class="text-sm text-muted">8 / 10</span>
                        </div>
                        <div class="progress mb-2" style="height: 10px;">
                            <div class="progress-bar bg-primary" role="progressbar" style="width: 80%" id="progresso-produtos"></div>
                        </div>
                        <small class="text-muted">80% da meta atingida</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-bolt me-2"></i>
                    Ações Rápidas
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('venda_nova') }}" class="btn btn-success">
                        <i class="fas fa-shopping-cart me-2"></i>
                        Nova Venda
                    </a>
                    <a href="{{ url_for('agendamento_novo') }}" class="btn btn-info">
                        <i class="fas fa-calendar-plus me-2"></i>
                        Novo Agendamento
                    </a>
                    <a href="{{ url_for('cliente_novo') }}" class="btn btn-primary">
                        <i class="fas fa-user-plus me-2"></i>
                        Novo Cliente
                    </a>
                    <a href="{{ url_for('produto_novo') }}" class="btn btn-warning">
                        <i class="fas fa-plus-circle me-2"></i>
                        Novo Produto
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Alertas do Sistema -->
<div class="row mb-4" id="alertas-sistema">
    <!-- Alertas serão carregados via JavaScript -->
</div>

<!-- Últimos Agendamentos -->
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-calendar-alt me-2"></i>
                    Últimos Agendamentos
                </h6>
                <a href="{{ url_for('agendamentos') }}" class="btn btn-sm btn-outline-primary">
                    Ver Todos
                </a>
            </div>
            <div class="card-body">
                {% if ultimos_agendamentos %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Cliente</th>
                                <th>Produto</th>
                                <th>Vendedor</th>
                                <th>Data/Hora</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for agendamento in ultimos_agendamentos %}
                            <tr>
                                <td>
                                    <i class="fas fa-user me-2 text-muted"></i>
                                    {{ agendamento.cliente_nome }}
                                </td>
                                <td>
                                    <i class="fas fa-box me-2 text-muted"></i>
                                    {{ agendamento.produto_nome }}
                                </td>
                                <td>
                                    <i class="fas fa-user-tie me-2 text-muted"></i>
                                    {{ agendamento.vendedor_nome }}
                                </td>
                                <td>
                                    <i class="fas fa-clock me-2 text-muted"></i>
                                    {{ agendamento.data_agendamento }}
                                </td>
                                <td>
                                    <span class="badge bg-{{ 'success' if agendamento.status == 'confirmado' else 'primary' if agendamento.status == 'agendado' else 'secondary' }}">
                                        {{ agendamento.status.title() }}
                                    </span>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                    <p class="text-muted">Nenhum agendamento encontrado.</p>
                    <a href="{{ url_for('agendamento_novo') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        Criar Primeiro Agendamento
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}
.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}
.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}
.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}
.text-xs {
    font-size: 0.7rem;
}
.text-gray-800 {
    color: #5a5c69 !important;
}
.text-gray-300 {
    color: #dddfeb !important;
}
</style>
{% endblock %}

{% block extra_js %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/locale/pt-br.min.js"></script>
<script>
moment.locale('pt-br');
document.getElementById('data-atual').textContent = moment().format('DD/MM/YYYY');

// Sistema de Alertas
async function carregarAlertas() {
    const alertasContainer = document.getElementById('alertas-sistema');

    try {
        const response = await fetch('/api/alertas');
        const data = await response.json();
        const alertas = data.alertas;

        // Verificar se há alertas para mostrar
        if (alertas.length === 0) {
            alertasContainer.style.display = 'none';
            return;
        }

        let alertasHtml = '';
        alertas.forEach(alerta => {
            alertasHtml += `
                <div class="col-md-6 mb-3">
                    <div class="alert alert-${alerta.cor} alert-dismissible fade show" role="alert">
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                <i class="fas fa-${alerta.icone} fa-2x"></i>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="alert-heading mb-1">${alerta.titulo}</h6>
                                <p class="mb-2">${alerta.mensagem}</p>
                                <a href="${alerta.link}" class="btn btn-sm btn-outline-${alerta.cor}">
                                    ${alerta.acao}
                                </a>
                            </div>
                        </div>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                </div>
            `;
        });

        alertasContainer.innerHTML = alertasHtml;
    } catch (error) {
        console.error('Erro ao carregar alertas:', error);
        alertasContainer.style.display = 'none';
    }
}

// Verificar notificações em tempo real
function verificarNotificacoes() {
    // Simular verificação de notificações
    const agora = new Date();
    const hora = agora.getHours();

    // Notificação de bom dia
    if (hora >= 8 && hora < 12) {
        const ultimaNotificacao = localStorage.getItem('ultima-notificacao-bomdia');
        const hoje = agora.toDateString();

        if (ultimaNotificacao !== hoje) {
            setTimeout(() => {
                SaudeFlex.notify.info('Bom dia! Tenha um ótimo dia de vendas! 🌅', {
                    duration: 8000
                });
                localStorage.setItem('ultima-notificacao-bomdia', hoje);
            }, 2000);
        }
    }

    // Verificar estoque baixo (simulado)
    setTimeout(() => {
        const estoquesBaixos = Math.floor(Math.random() * 3) + 1; // 1-3 produtos
        if (estoquesBaixos > 0) {
            SaudeFlex.notify.warning(`${estoquesBaixos} produto(s) com estoque baixo!`, {
                duration: 10000
            });
        }
    }, 5000);
}

// Atualizar estatísticas em tempo real
async function atualizarEstatisticas() {
    try {
        const response = await fetch('/api/dashboard-stats');
        const stats = await response.json();

        // Atualizar cards de estatísticas
        const cards = document.querySelectorAll('.card .h5.mb-0.font-weight-bold.text-gray-800');
        if (cards.length >= 4) {
            cards[0].textContent = stats.total_produtos;
            cards[1].textContent = stats.total_clientes;
            cards[2].textContent = stats.agendamentos_hoje;
            cards[3].textContent = stats.estoque_baixo;
        }

        // Mostrar notificação se houver mudanças críticas
        if (stats.estoque_baixo > 0) {
            // Verificar se já foi notificado hoje
            const ultimaNotificacao = localStorage.getItem('ultima-notificacao-estoque');
            const hoje = new Date().toDateString();

            if (ultimaNotificacao !== hoje) {
                SaudeFlex.notify.warning(`${stats.estoque_baixo} produto(s) com estoque baixo!`, {
                    duration: 10000
                });
                localStorage.setItem('ultima-notificacao-estoque', hoje);
            }
        }

        console.log('Estatísticas atualizadas:', stats);
    } catch (error) {
        console.error('Erro ao atualizar estatísticas:', error);
    }
}

// Inicialização
document.addEventListener('DOMContentLoaded', function() {
    carregarAlertas();
    verificarNotificacoes();

    // Atualizar estatísticas a cada 30 segundos
    setInterval(atualizarEstatisticas, 30000);

    // Verificar notificações a cada 5 minutos
    setInterval(verificarNotificacoes, 300000);
});
</script>
{% endblock %}
