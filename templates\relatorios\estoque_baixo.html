{% extends "base.html" %}

{% block title %}Relatório de Estoque Baixo - Saúde Flex{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-exclamation-triangle me-2 text-warning"></i>
                Relatório de Estoque Baixo
            </h1>
            <div class="btn-group" role="group">
                <a href="{{ url_for('relatorios') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>
                    Voltar
                </a>
                <button type="button" class="btn btn-outline-primary" onclick="exportarRelatorio()">
                    <i class="fas fa-download me-2"></i>
                    Exportar PDF
                </button>
                <button type="button" class="btn btn-outline-success" onclick="enviarPorEmail()">
                    <i class="fas fa-envelope me-2"></i>
                    Enviar por Email
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Resumo -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Produtos em Alerta
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ produtos|length }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-left-danger shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                            Estoque Crítico
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="estoque-critico">
                            0
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-times-circle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Valor Total em Risco
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="valor-risco">
                            R$ 0,00
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Última Atualização
                        </div>
                        <div class="h6 mb-0 font-weight-bold text-gray-800" id="ultima-atualizacao">
                            Agora
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-sync fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filtros -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="filtro-categoria">Categoria</label>
                            <select class="form-control" id="filtro-categoria">
                                <option value="">Todas as categorias</option>
                                <option value="bem-estar">Bem-estar</option>
                                <option value="saude">Saúde</option>
                                <option value="beleza">Beleza</option>
                                <option value="suplementos">Suplementos</option>
                                <option value="equipamentos">Equipamentos</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="filtro-criticidade">Nível de Criticidade</label>
                            <select class="form-control" id="filtro-criticidade">
                                <option value="">Todos os níveis</option>
                                <option value="critico">Crítico (estoque = 0)</option>
                                <option value="baixo">Baixo (abaixo do mínimo)</option>
                                <option value="atencao">Atenção (próximo ao mínimo)</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="filtro-ordenacao">Ordenar por</label>
                            <select class="form-control" id="filtro-ordenacao">
                                <option value="estoque">Menor estoque primeiro</option>
                                <option value="nome">Nome do produto</option>
                                <option value="categoria">Categoria</option>
                                <option value="valor">Maior valor primeiro</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Tabela de Produtos -->
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-list me-2"></i>
                    Produtos com Estoque Baixo
                </h6>
            </div>
            <div class="card-body">
                {% if produtos %}
                <div class="table-responsive">
                    <table class="table table-hover" id="tabela-estoque">
                        <thead>
                            <tr>
                                <th>Produto</th>
                                <th>Categoria</th>
                                <th>Estoque Atual</th>
                                <th>Estoque Mínimo</th>
                                <th>Diferença</th>
                                <th>Preço de Venda</th>
                                <th>Status</th>
                                <th>Ações</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for produto in produtos %}
                            <tr data-categoria="{{ produto.categoria_nome|lower if produto.categoria_nome else '' }}" 
                                data-criticidade="{{ 'critico' if produto.estoque_atual == 0 else 'baixo' }}">
                                <td>
                                    <div class="d-flex align-items-center">
                                        {% if produto.foto %}
                                        <img src="{{ url_for('static', filename='img/produtos/' + produto.foto) }}" 
                                             alt="{{ produto.nome }}" class="rounded me-2" style="width: 40px; height: 40px; object-fit: cover;">
                                        {% else %}
                                        <div class="bg-light rounded me-2 d-flex align-items-center justify-content-center" 
                                             style="width: 40px; height: 40px;">
                                            <i class="fas fa-box text-muted"></i>
                                        </div>
                                        {% endif %}
                                        <div>
                                            <strong>{{ produto.nome }}</strong>
                                            {% if produto.descricao %}
                                            <br><small class="text-muted">{{ produto.descricao[:30] }}{% if produto.descricao|length > 30 %}...{% endif %}</small>
                                            {% endif %}
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    {% if produto.categoria_nome %}
                                    <span class="badge bg-secondary">{{ produto.categoria_nome }}</span>
                                    {% else %}
                                    <span class="text-muted">Sem categoria</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="h6 mb-0 {{ 'text-danger' if produto.estoque_atual == 0 else 'text-warning' }}">
                                        {{ produto.estoque_atual }}
                                    </span>
                                </td>
                                <td>
                                    <span class="text-muted">{{ produto.estoque_minimo }}</span>
                                </td>
                                <td>
                                    {% set diferenca = produto.estoque_minimo - produto.estoque_atual %}
                                    <span class="text-danger">
                                        <i class="fas fa-arrow-down me-1"></i>
                                        {{ diferenca }}
                                    </span>
                                </td>
                                <td>
                                    <span class="text-success">R$ {{ "%.2f"|format(produto.preco_venda) }}</span>
                                </td>
                                <td>
                                    {% if produto.estoque_atual == 0 %}
                                    <span class="badge bg-danger">
                                        <i class="fas fa-times me-1"></i>
                                        Crítico
                                    </span>
                                    {% else %}
                                    <span class="badge bg-warning">
                                        <i class="fas fa-exclamation-triangle me-1"></i>
                                        Baixo
                                    </span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-sm btn-outline-primary" 
                                                onclick="verProduto({{ produto.id }})" title="Ver Produto">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-success" 
                                                onclick="atualizarEstoque({{ produto.id }})" title="Atualizar Estoque">
                                            <i class="fas fa-plus"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-info" 
                                                onclick="adicionarCompra({{ produto.id }})" title="Adicionar à Lista de Compras">
                                            <i class="fas fa-shopping-cart"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                    <h5 class="text-success">Parabéns!</h5>
                    <p class="text-muted">Todos os produtos estão com estoque adequado.</p>
                    <a href="{{ url_for('produtos') }}" class="btn btn-primary">
                        <i class="fas fa-box me-2"></i>
                        Ver Todos os Produtos
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Modal de Atualização de Estoque -->
<div class="modal fade" id="modalAtualizarEstoque" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-plus me-2"></i>
                    Atualizar Estoque
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="form-group mb-3">
                    <label for="produto-nome" class="form-label">Produto</label>
                    <input type="text" class="form-control" id="produto-nome" readonly>
                </div>
                <div class="form-group mb-3">
                    <label for="estoque-atual" class="form-label">Estoque Atual</label>
                    <input type="number" class="form-control" id="estoque-atual" readonly>
                </div>
                <div class="form-group mb-3">
                    <label for="quantidade-adicionar" class="form-label">Quantidade a Adicionar</label>
                    <input type="number" class="form-control" id="quantidade-adicionar" min="1" required>
                </div>
                <div class="form-group mb-3">
                    <label for="observacoes-estoque" class="form-label">Observações</label>
                    <textarea class="form-control" id="observacoes-estoque" rows="3" placeholder="Motivo da atualização..."></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-success" onclick="confirmarAtualizacaoEstoque()">
                    <i class="fas fa-save me-2"></i>
                    Atualizar Estoque
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}
.border-left-danger {
    border-left: 0.25rem solid #e74a3b !important;
}
.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}
.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}
.text-xs {
    font-size: 0.7rem;
}
.text-gray-800 {
    color: #5a5c69 !important;
}
.text-gray-300 {
    color: #dddfeb !important;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
let produtoAtualEstoque = null;

// Filtros
document.getElementById('filtro-categoria').addEventListener('change', filtrarTabela);
document.getElementById('filtro-criticidade').addEventListener('change', filtrarTabela);
document.getElementById('filtro-ordenacao').addEventListener('change', ordenarTabela);

function filtrarTabela() {
    const categoria = document.getElementById('filtro-categoria').value;
    const criticidade = document.getElementById('filtro-criticidade').value;
    
    const linhas = document.querySelectorAll('#tabela-estoque tbody tr');
    
    linhas.forEach(linha => {
        let mostrar = true;
        
        if (categoria && linha.dataset.categoria !== categoria) {
            mostrar = false;
        }
        
        if (criticidade && linha.dataset.criticidade !== criticidade) {
            mostrar = false;
        }
        
        linha.style.display = mostrar ? '' : 'none';
    });
    
    atualizarResumo();
}

function ordenarTabela() {
    // Implementar ordenação
    SaudeFlex.notify.info('Ordenação será implementada em breve!');
}

function verProduto(id) {
    window.location.href = '/produtos/' + id;
}

function atualizarEstoque(id) {
    produtoAtualEstoque = id;
    
    // Simular dados do produto
    document.getElementById('produto-nome').value = 'Produto Exemplo';
    document.getElementById('estoque-atual').value = '2';
    document.getElementById('quantidade-adicionar').value = '';
    document.getElementById('observacoes-estoque').value = '';
    
    const modal = new bootstrap.Modal(document.getElementById('modalAtualizarEstoque'));
    modal.show();
}

function confirmarAtualizacaoEstoque() {
    const quantidade = parseInt(document.getElementById('quantidade-adicionar').value);
    const observacoes = document.getElementById('observacoes-estoque').value;
    
    if (!quantidade || quantidade <= 0) {
        SaudeFlex.notify.error('Informe uma quantidade válida.');
        return;
    }
    
    // Implementar atualização de estoque
    SaudeFlex.notify.success('Estoque atualizado com sucesso!');
    
    const modal = bootstrap.Modal.getInstance(document.getElementById('modalAtualizarEstoque'));
    modal.hide();
    
    // Recarregar página
    setTimeout(() => {
        location.reload();
    }, 1500);
}

function adicionarCompra(id) {
    SaudeFlex.notify.success('Produto adicionado à lista de compras!');
}

function exportarRelatorio() {
    SaudeFlex.notify.info('Gerando relatório PDF...');
    // Simular download
    setTimeout(() => {
        SaudeFlex.notify.success('Relatório exportado com sucesso!');
    }, 2000);
}

function enviarPorEmail() {
    SaudeFlex.notify.info('Enviando relatório por email...');
    // Simular envio
    setTimeout(() => {
        SaudeFlex.notify.success('Relatório enviado por email!');
    }, 2000);
}

function atualizarResumo() {
    // Atualizar contadores do resumo
    const linhasVisiveis = document.querySelectorAll('#tabela-estoque tbody tr[style=""]');
    const estoqueCritico = Array.from(linhasVisiveis).filter(linha => 
        linha.dataset.criticidade === 'critico'
    ).length;
    
    document.getElementById('estoque-critico').textContent = estoqueCritico;
}

// Inicialização
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('ultima-atualizacao').textContent = new Date().toLocaleString('pt-BR');
    atualizarResumo();
});
</script>
{% endblock %}
