#!/usr/bin/env python3
"""
Saúde Flex - Sistema Completo de Agendamentos e Vendas
Versão Final Funcional
"""

from flask import Flask, render_template_string, request, redirect, url_for, session, flash, jsonify
from datetime import datetime
import os
import sqlite3
import bcrypt
import json
import webbrowser
import threading
import time

# Configurações
DATABASE = 'saude_flex.db'
SECRET_KEY = 'saude_flex_secret_key_2024'
PORT = 8080

# Criar app Flask
app = Flask(__name__)
app.secret_key = SECRET_KEY

def init_database():
    """Inicializar banco de dados"""
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    # Tabela de usuários
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS usuarios (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            nome TEXT NOT NULL,
            email TEXT UNIQUE NOT NULL,
            senha TEXT NOT NULL,
            tipo TEXT NOT NULL DEFAULT 'vendedor',
            ativo INTEGER DEFAULT 1,
            data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # Tabela de categorias
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS categorias (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            nome TEXT NOT NULL,
            descricao TEXT,
            ativo INTEGER DEFAULT 1
        )
    ''')
    
    # Tabela de produtos
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS produtos (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            nome TEXT NOT NULL,
            descricao TEXT,
            categoria_id INTEGER,
            preco_custo REAL NOT NULL,
            tipo_lucro TEXT NOT NULL DEFAULT 'percentual',
            valor_lucro REAL NOT NULL DEFAULT 0,
            preco_venda REAL NOT NULL,
            estoque_atual INTEGER DEFAULT 0,
            estoque_minimo INTEGER DEFAULT 5,
            ativo INTEGER DEFAULT 1,
            data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (categoria_id) REFERENCES categorias (id)
        )
    ''')
    
    # Tabela de clientes
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS clientes (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            nome TEXT NOT NULL,
            email TEXT,
            telefone TEXT,
            cpf TEXT,
            endereco TEXT,
            cidade TEXT,
            estado TEXT,
            cep TEXT,
            data_nascimento DATE,
            observacoes TEXT,
            ativo INTEGER DEFAULT 1,
            data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # Tabela de vendas
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS vendas (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            cliente_id INTEGER NOT NULL,
            vendedor_id INTEGER NOT NULL,
            subtotal REAL NOT NULL,
            desconto_total REAL DEFAULT 0,
            total_final REAL NOT NULL,
            observacoes TEXT,
            data_venda TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (cliente_id) REFERENCES clientes (id),
            FOREIGN KEY (vendedor_id) REFERENCES usuarios (id)
        )
    ''')
    
    # Verificar se já existe um admin
    cursor.execute("SELECT id FROM usuarios WHERE tipo = 'admin' LIMIT 1")
    if not cursor.fetchone():
        # Criar usuário admin padrão
        senha_hash = bcrypt.hashpw('admin123'.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
        cursor.execute('''
            INSERT INTO usuarios (nome, email, senha, tipo)
            VALUES (?, ?, ?, ?)
        ''', ('Administrador', '<EMAIL>', senha_hash, 'admin'))
        print("✅ Usuário admin criado: <EMAIL> / admin123")
    
    # Criar dados de exemplo
    cursor.execute("SELECT COUNT(*) as total FROM categorias")
    if cursor.fetchone()['total'] == 0:
        cursor.execute("INSERT INTO categorias (nome, descricao) VALUES (?, ?)", 
                      ('Suplementos', 'Suplementos alimentares'))
        cursor.execute("INSERT INTO categorias (nome, descricao) VALUES (?, ?)", 
                      ('Cosméticos', 'Produtos de beleza'))
        print("✅ Categorias de exemplo criadas")
    
    cursor.execute("SELECT COUNT(*) as total FROM produtos")
    if cursor.fetchone()['total'] == 0:
        cursor.execute('''
            INSERT INTO produtos (nome, descricao, categoria_id, preco_custo, preco_venda, estoque_atual)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', ('Whey Protein', 'Proteína em pó', 1, 50.00, 89.90, 20))
        cursor.execute('''
            INSERT INTO produtos (nome, descricao, categoria_id, preco_custo, preco_venda, estoque_atual)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', ('Creatina', 'Creatina monohidratada', 1, 30.00, 59.90, 15))
        print("✅ Produtos de exemplo criados")
    
    cursor.execute("SELECT COUNT(*) as total FROM clientes")
    if cursor.fetchone()['total'] == 0:
        cursor.execute('''
            INSERT INTO clientes (nome, email, telefone, cidade)
            VALUES (?, ?, ?, ?)
        ''', ('João Silva', '<EMAIL>', '(11) 99999-9999', 'São Paulo'))
        cursor.execute('''
            INSERT INTO clientes (nome, email, telefone, cidade)
            VALUES (?, ?, ?, ?)
        ''', ('Maria Santos', '<EMAIL>', '(11) 88888-8888', 'Rio de Janeiro'))
        print("✅ Clientes de exemplo criados")
    
    conn.commit()
    conn.close()

def get_db():
    """Obter conexão com banco"""
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    return conn

def login_required(f):
    """Decorator para verificar login"""
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    decorated_function.__name__ = f.__name__
    return decorated_function

# Rotas
@app.route('/')
@login_required
def index():
    """Dashboard principal"""
    conn = get_db()
    cursor = conn.cursor()
    
    # Estatísticas
    cursor.execute("SELECT COUNT(*) as total FROM produtos WHERE ativo = 1")
    total_produtos = cursor.fetchone()['total']
    
    cursor.execute("SELECT COUNT(*) as total FROM clientes WHERE ativo = 1")
    total_clientes = cursor.fetchone()['total']
    
    cursor.execute("SELECT COUNT(*) as total FROM vendas WHERE DATE(data_venda) = DATE('now')")
    vendas_hoje = cursor.fetchone()['total']
    
    cursor.execute("SELECT COALESCE(SUM(total_final), 0) as total FROM vendas WHERE DATE(data_venda) = DATE('now')")
    faturamento_hoje = cursor.fetchone()['total']
    
    # Produtos recentes
    cursor.execute("SELECT * FROM produtos WHERE ativo = 1 ORDER BY data_criacao DESC LIMIT 5")
    produtos_recentes = [dict(row) for row in cursor.fetchall()]
    
    # Clientes recentes
    cursor.execute("SELECT * FROM clientes WHERE ativo = 1 ORDER BY data_criacao DESC LIMIT 5")
    clientes_recentes = [dict(row) for row in cursor.fetchall()]
    
    conn.close()
    
    return render_template_string(TEMPLATE_DASHBOARD, 
                                total_produtos=total_produtos,
                                total_clientes=total_clientes,
                                vendas_hoje=vendas_hoje,
                                faturamento_hoje=faturamento_hoje,
                                produtos_recentes=produtos_recentes,
                                clientes_recentes=clientes_recentes)

@app.route('/login', methods=['GET', 'POST'])
def login():
    """Página de login"""
    if request.method == 'POST':
        email = request.form['email']
        senha = request.form['senha']
        
        conn = get_db()
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM usuarios WHERE email = ? AND ativo = 1', (email,))
        usuario = cursor.fetchone()
        conn.close()
        
        if usuario and bcrypt.checkpw(senha.encode('utf-8'), usuario['senha'].encode('utf-8')):
            session['user_id'] = usuario['id']
            session['user_nome'] = usuario['nome']
            session['user_tipo'] = usuario['tipo']
            session['user_email'] = usuario['email']
            
            flash('Login realizado com sucesso!', 'success')
            return redirect(url_for('index'))
        else:
            flash('Email ou senha incorretos!', 'error')
    
    return render_template_string(TEMPLATE_LOGIN)

@app.route('/logout')
def logout():
    """Logout"""
    session.clear()
    flash('Logout realizado com sucesso!', 'success')
    return redirect(url_for('login'))

@app.route('/produtos')
@login_required
def produtos():
    """Lista de produtos"""
    conn = get_db()
    cursor = conn.cursor()
    cursor.execute('''
        SELECT p.*, c.nome as categoria_nome 
        FROM produtos p 
        LEFT JOIN categorias c ON p.categoria_id = c.id 
        WHERE p.ativo = 1 
        ORDER BY p.nome
    ''')
    produtos_lista = [dict(row) for row in cursor.fetchall()]
    conn.close()
    
    return render_template_string(TEMPLATE_PRODUTOS, produtos=produtos_lista)

@app.route('/clientes')
@login_required
def clientes():
    """Lista de clientes"""
    conn = get_db()
    cursor = conn.cursor()
    cursor.execute('SELECT * FROM clientes WHERE ativo = 1 ORDER BY nome')
    clientes_lista = [dict(row) for row in cursor.fetchall()]
    conn.close()
    
    return render_template_string(TEMPLATE_CLIENTES, clientes=clientes_lista)

def abrir_navegador():
    """Abrir navegador automaticamente"""
    time.sleep(2)
    webbrowser.open(f'http://localhost:{PORT}')

# Templates inline
TEMPLATE_LOGIN = '''
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Saúde Flex</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container">
        <div class="row justify-content-center mt-5">
            <div class="col-md-6">
                <div class="card shadow">
                    <div class="card-body">
                        <div class="text-center mb-4">
                            <i class="fas fa-heartbeat fa-3x text-primary"></i>
                            <h3 class="mt-2">Saúde Flex</h3>
                            <p class="text-muted">Sistema de Agendamentos e Vendas</p>
                        </div>

                        {% with messages = get_flashed_messages(with_categories=true) %}
                            {% if messages %}
                                {% for category, message in messages %}
                                    <div class="alert alert-{{ 'danger' if category == 'error' else 'success' }} alert-dismissible fade show">
                                        {{ message }}
                                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                    </div>
                                {% endfor %}
                            {% endif %}
                        {% endwith %}

                        <form method="POST">
                            <div class="mb-3">
                                <label for="email" class="form-label">Email</label>
                                <input type="email" class="form-control" id="email" name="email" required>
                            </div>
                            <div class="mb-3">
                                <label for="senha" class="form-label">Senha</label>
                                <input type="password" class="form-control" id="senha" name="senha" required>
                            </div>
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-sign-in-alt me-2"></i>Entrar
                            </button>
                        </form>

                        <div class="mt-4 text-center">
                            <small class="text-muted">
                                <strong>Login padrão:</strong><br>
                                Email: <EMAIL><br>
                                Senha: admin123
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
'''

TEMPLATE_DASHBOARD = '''
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Saúde Flex</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        .card { border: none; box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15); }
        .border-left-primary { border-left: 0.25rem solid #4e73df !important; }
        .border-left-success { border-left: 0.25rem solid #1cc88a !important; }
        .border-left-info { border-left: 0.25rem solid #36b9cc !important; }
        .border-left-warning { border-left: 0.25rem solid #f6c23e !important; }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="fas fa-heartbeat me-2"></i>Saúde Flex
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('produtos') }}">
                    <i class="fas fa-box me-1"></i>Produtos
                </a>
                <a class="nav-link" href="{{ url_for('clientes') }}">
                    <i class="fas fa-users me-1"></i>Clientes
                </a>
                <span class="navbar-text me-3">
                    <i class="fas fa-user me-1"></i>{{ session.user_nome }}
                </span>
                <a class="nav-link" href="{{ url_for('logout') }}">
                    <i class="fas fa-sign-out-alt me-1"></i>Sair
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <h1 class="h3 mb-4">
                    <i class="fas fa-tachometer-alt me-2 text-primary"></i>
                    Dashboard - Saúde Flex
                </h1>
            </div>
        </div>

        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else 'success' }} alert-dismissible fade show">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <!-- Estatísticas -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-primary shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                    Total de Produtos
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    {{ total_produtos }}
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-box fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-success shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                    Total de Clientes
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    {{ total_clientes }}
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-users fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-info shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                    Vendas Hoje
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    {{ vendas_hoje }}
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-warning shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                    Faturamento Hoje
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    R$ {{ "%.2f"|format(faturamento_hoje) }}
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Produtos e Clientes Recentes -->
        <div class="row">
            <div class="col-lg-6">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-box me-2"></i>Produtos Recentes
                        </h6>
                    </div>
                    <div class="card-body">
                        {% if produtos_recentes %}
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Nome</th>
                                            <th>Preço</th>
                                            <th>Estoque</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for produto in produtos_recentes %}
                                        <tr>
                                            <td>{{ produto.nome }}</td>
                                            <td>R$ {{ "%.2f"|format(produto.preco_venda) }}</td>
                                            <td>
                                                <span class="badge bg-{{ 'danger' if produto.estoque_atual <= produto.estoque_minimo else 'success' }}">
                                                    {{ produto.estoque_atual }}
                                                </span>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <p class="text-muted">Nenhum produto cadastrado</p>
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="col-lg-6">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-users me-2"></i>Clientes Recentes
                        </h6>
                    </div>
                    <div class="card-body">
                        {% if clientes_recentes %}
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Nome</th>
                                            <th>Email</th>
                                            <th>Telefone</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for cliente in clientes_recentes %}
                                        <tr>
                                            <td>{{ cliente.nome }}</td>
                                            <td>{{ cliente.email or '-' }}</td>
                                            <td>{{ cliente.telefone or '-' }}</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <p class="text-muted">Nenhum cliente cadastrado</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
'''

TEMPLATE_PRODUTOS = '''
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Produtos - Saúde Flex</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="fas fa-heartbeat me-2"></i>Saúde Flex
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">
                    <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                </a>
                <a class="nav-link" href="{{ url_for('clientes') }}">
                    <i class="fas fa-users me-1"></i>Clientes
                </a>
                <span class="navbar-text me-3">
                    <i class="fas fa-user me-1"></i>{{ session.user_nome }}
                </span>
                <a class="nav-link" href="{{ url_for('logout') }}">
                    <i class="fas fa-sign-out-alt me-1"></i>Sair
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <h1 class="h3 mb-4">
                    <i class="fas fa-box me-2 text-primary"></i>
                    Produtos
                </h1>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Lista de Produtos</h6>
                    </div>
                    <div class="card-body">
                        {% if produtos %}
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Nome</th>
                                            <th>Categoria</th>
                                            <th>Preço Custo</th>
                                            <th>Preço Venda</th>
                                            <th>Estoque</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for produto in produtos %}
                                        <tr>
                                            <td>
                                                <strong>{{ produto.nome }}</strong>
                                                {% if produto.descricao %}
                                                <br><small class="text-muted">{{ produto.descricao }}</small>
                                                {% endif %}
                                            </td>
                                            <td>{{ produto.categoria_nome or 'Sem categoria' }}</td>
                                            <td>R$ {{ "%.2f"|format(produto.preco_custo) }}</td>
                                            <td><strong>R$ {{ "%.2f"|format(produto.preco_venda) }}</strong></td>
                                            <td>
                                                <span class="badge bg-{{ 'danger' if produto.estoque_atual <= produto.estoque_minimo else 'success' }}">
                                                    {{ produto.estoque_atual }}
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-success">Ativo</span>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <div class="text-center py-5">
                                <i class="fas fa-box fa-3x text-muted mb-3"></i>
                                <h5>Nenhum produto cadastrado</h5>
                                <p class="text-muted">Cadastre produtos para começar a usar o sistema.</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
'''

TEMPLATE_CLIENTES = '''
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clientes - Saúde Flex</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="fas fa-heartbeat me-2"></i>Saúde Flex
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">
                    <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                </a>
                <a class="nav-link" href="{{ url_for('produtos') }}">
                    <i class="fas fa-box me-1"></i>Produtos
                </a>
                <span class="navbar-text me-3">
                    <i class="fas fa-user me-1"></i>{{ session.user_nome }}
                </span>
                <a class="nav-link" href="{{ url_for('logout') }}">
                    <i class="fas fa-sign-out-alt me-1"></i>Sair
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <h1 class="h3 mb-4">
                    <i class="fas fa-users me-2 text-primary"></i>
                    Clientes
                </h1>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Lista de Clientes</h6>
                    </div>
                    <div class="card-body">
                        {% if clientes %}
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Nome</th>
                                            <th>Email</th>
                                            <th>Telefone</th>
                                            <th>Cidade</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for cliente in clientes %}
                                        <tr>
                                            <td>
                                                <strong>{{ cliente.nome }}</strong>
                                                {% if cliente.cpf %}
                                                <br><small class="text-muted">CPF: {{ cliente.cpf }}</small>
                                                {% endif %}
                                            </td>
                                            <td>{{ cliente.email or '-' }}</td>
                                            <td>{{ cliente.telefone or '-' }}</td>
                                            <td>{{ cliente.cidade or '-' }}</td>
                                            <td>
                                                <span class="badge bg-success">Ativo</span>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <div class="text-center py-5">
                                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                <h5>Nenhum cliente cadastrado</h5>
                                <p class="text-muted">Cadastre clientes para começar a usar o sistema.</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
'''

if __name__ == '__main__':
    print("🚀 Iniciando Saúde Flex...")
    print("📁 Inicializando banco de dados...")

    try:
        init_database()
        print("✅ Banco de dados inicializado")
        print("🌐 Iniciando servidor Flask...")
        print(f"📍 Acesse: http://localhost:{PORT}")
        print("🔑 Login: <EMAIL> / admin123")
        print("-" * 50)

        # Abrir navegador automaticamente
        threading.Thread(target=abrir_navegador, daemon=True).start()

        app.run(debug=False, host='127.0.0.1', port=PORT)

    except Exception as e:
        print(f"❌ Erro ao iniciar: {e}")
        import traceback
        traceback.print_exc()
        input("Pressione Enter para sair...")
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Saúde Flex{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        .sidebar { min-height: 100vh; background: #f8f9fa; }
        .card { border: none; box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15); }
        .border-left-primary { border-left: 0.25rem solid #4e73df !important; }
        .border-left-success { border-left: 0.25rem solid #1cc88a !important; }
        .border-left-info { border-left: 0.25rem solid #36b9cc !important; }
        .border-left-warning { border-left: 0.25rem solid #f6c23e !important; }
    </style>
</head>
<body>
    {% if session.user_id %}
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="fas fa-heartbeat me-2"></i>Saúde Flex
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">
                    <i class="fas fa-user me-1"></i>{{ session.user_nome }}
                </span>
                <a class="nav-link" href="{{ url_for('logout') }}">
                    <i class="fas fa-sign-out-alt me-1"></i>Sair
                </a>
            </div>
        </div>
    </nav>
    {% endif %}
    
    <div class="container-fluid">
        <div class="row">
            {% if session.user_id %}
            <nav class="col-md-2 d-md-block sidebar">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="/">
                                <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('produtos') }}">
                                <i class="fas fa-box me-2"></i>Produtos
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('clientes') }}">
                                <i class="fas fa-users me-2"></i>Clientes
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>
            <main class="col-md-10 ms-sm-auto px-md-4">
            {% else %}
            <main class="col-12">
            {% endif %}
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ 'danger' if category == 'error' else 'success' }} alert-dismissible fade show mt-3">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}
                
                {% block content %}{% endblock %}
            </main>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
'''
