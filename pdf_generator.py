"""
Gerador de PDF para comprovantes de venda
"""

import os
from datetime import datetime
from reportlab.lib.pagesizes import A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib import colors
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
from reportlab.lib.enums import TA_CENTER, TA_RIGHT, TA_LEFT
from config import Config

class PDFGenerator:
    def __init__(self):
        self.page_width, self.page_height = A4
        self.styles = getSampleStyleSheet()
        self._setup_custom_styles()
    
    def _setup_custom_styles(self):
        """Configurar estilos personalizados"""
        # Estilo para título principal
        self.styles.add(ParagraphStyle(
            name='CustomTitle',
            parent=self.styles['Heading1'],
            fontSize=24,
            spaceAfter=30,
            alignment=TA_CENTER,
            textColor=colors.HexColor('#007bff')
        ))
        
        # Estilo para subtítulo
        self.styles.add(ParagraphStyle(
            name='CustomSubtitle',
            parent=self.styles['Normal'],
            fontSize=12,
            spaceAfter=20,
            alignment=TA_CENTER,
            textColor=colors.grey
        ))
        
        # Estilo para seções
        self.styles.add(ParagraphStyle(
            name='SectionTitle',
            parent=self.styles['Heading2'],
            fontSize=14,
            spaceBefore=20,
            spaceAfter=10,
            textColor=colors.HexColor('#495057')
        ))
        
        # Estilo para total
        self.styles.add(ParagraphStyle(
            name='TotalStyle',
            parent=self.styles['Normal'],
            fontSize=16,
            alignment=TA_RIGHT,
            textColor=colors.HexColor('#28a745'),
            fontName='Helvetica-Bold'
        ))
    
    def gerar_comprovante_venda(self, venda, output_path=None):
        """
        Gerar comprovante de venda em PDF
        
        Args:
            venda (dict): Dados da venda
            output_path (str): Caminho para salvar o PDF
        
        Returns:
            str: Caminho do arquivo PDF gerado
        """
        if output_path is None:
            # Criar pasta de comprovantes se não existir
            comprovantes_dir = os.path.join(Config.get_base_path(), 'comprovantes')
            os.makedirs(comprovantes_dir, exist_ok=True)
            
            # Nome do arquivo
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f'comprovante_venda_{venda["id"]}_{timestamp}.pdf'
            output_path = os.path.join(comprovantes_dir, filename)
        
        # Criar documento PDF
        doc = SimpleDocTemplate(
            output_path,
            pagesize=A4,
            rightMargin=72,
            leftMargin=72,
            topMargin=72,
            bottomMargin=18
        )
        
        # Construir conteúdo
        story = []
        
        # Cabeçalho
        story.append(Paragraph("SAÚDE FLEX", self.styles['CustomTitle']))
        story.append(Paragraph("Sistema de Agendamentos e Vendas", self.styles['CustomSubtitle']))
        story.append(Paragraph(f"COMPROVANTE DE VENDA #{venda['id']}", self.styles['SectionTitle']))
        story.append(Spacer(1, 20))
        
        # Informações da venda
        info_data = [
            ['Data da Venda:', venda.get('data_venda', 'N/A')],
            ['Vendedor:', venda.get('vendedor_nome', 'N/A')],
            ['Cliente:', venda.get('cliente_nome', 'N/A')],
        ]
        
        if venda.get('cliente_telefone'):
            info_data.append(['Telefone:', venda['cliente_telefone']])
        
        if venda.get('observacoes'):
            info_data.append(['Observações:', venda['observacoes']])
        
        info_table = Table(info_data, colWidths=[2*inch, 4*inch])
        info_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
        ]))
        
        story.append(info_table)
        story.append(Spacer(1, 30))
        
        # Tabela de itens
        story.append(Paragraph("Itens da Venda", self.styles['SectionTitle']))
        
        # Cabeçalho da tabela
        items_data = [['Produto', 'Qtd', 'Preço Unit.', 'Desconto', 'Subtotal']]
        
        # Itens
        for item in venda.get('itens', []):
            desconto = f"R$ {item.get('desconto_item', 0):.2f}" if item.get('desconto_item', 0) > 0 else '-'
            items_data.append([
                item.get('produto_nome', ''),
                str(item.get('quantidade', 0)),
                f"R$ {item.get('preco_unitario', 0):.2f}",
                desconto,
                f"R$ {item.get('subtotal_item', 0):.2f}"
            ])
        
        items_table = Table(items_data, colWidths=[2.5*inch, 0.8*inch, 1*inch, 1*inch, 1*inch])
        items_table.setStyle(TableStyle([
            # Cabeçalho
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#f8f9fa')),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.HexColor('#495057')),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            
            # Dados
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 1), (-1, -1), 9),
            ('ALIGN', (1, 1), (-1, -1), 'RIGHT'),
            
            # Bordas
            ('GRID', (0, 0), (-1, -1), 1, colors.HexColor('#dee2e6')),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
            ('TOPPADDING', (0, 0), (-1, -1), 8),
        ]))
        
        story.append(items_table)
        story.append(Spacer(1, 30))
        
        # Totais
        totals_data = [
            ['Subtotal:', f"R$ {venda.get('subtotal', 0):.2f}"]
        ]
        
        if venda.get('desconto_total', 0) > 0:
            totals_data.append(['Desconto Total:', f"-R$ {venda['desconto_total']:.2f}"])
        
        totals_data.append(['TOTAL FINAL:', f"R$ {venda.get('total_final', 0):.2f}"])
        
        totals_table = Table(totals_data, colWidths=[3*inch, 1.5*inch])
        totals_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
            ('FONTNAME', (0, 0), (0, -2), 'Helvetica'),
            ('FONTNAME', (0, -1), (-1, -1), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -2), 10),
            ('FONTSIZE', (0, -1), (-1, -1), 14),
            ('TEXTCOLOR', (0, -1), (-1, -1), colors.HexColor('#28a745')),
            ('LINEABOVE', (0, -1), (-1, -1), 2, colors.HexColor('#28a745')),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
        ]))
        
        # Alinhar tabela à direita
        totals_table.hAlign = 'RIGHT'
        story.append(totals_table)
        story.append(Spacer(1, 50))
        
        # Área de assinaturas
        signature_data = [
            ['_' * 30, '_' * 30],
            ['Assinatura do Cliente', 'Assinatura do Vendedor']
        ]
        
        signature_table = Table(signature_data, colWidths=[2.5*inch, 2.5*inch])
        signature_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 20),
        ]))
        
        story.append(signature_table)
        story.append(Spacer(1, 30))
        
        # Rodapé
        footer_text = f"""
        <para align="center">
        Este documento comprova a realização da venda descrita acima.<br/>
        Saúde Flex - Sistema de Agendamentos e Vendas<br/>
        Gerado em {datetime.now().strftime('%d/%m/%Y às %H:%M')}
        </para>
        """
        
        story.append(Paragraph(footer_text, self.styles['Normal']))
        
        # Gerar PDF
        doc.build(story)
        
        return output_path
    
    def gerar_relatorio_vendas(self, vendas, periodo, output_path=None):
        """
        Gerar relatório de vendas em PDF
        
        Args:
            vendas (list): Lista de vendas
            periodo (str): Período do relatório
            output_path (str): Caminho para salvar o PDF
        
        Returns:
            str: Caminho do arquivo PDF gerado
        """
        if output_path is None:
            # Criar pasta de relatórios se não existir
            relatorios_dir = os.path.join(Config.get_base_path(), 'relatorios')
            os.makedirs(relatorios_dir, exist_ok=True)
            
            # Nome do arquivo
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f'relatorio_vendas_{timestamp}.pdf'
            output_path = os.path.join(relatorios_dir, filename)
        
        # Criar documento PDF
        doc = SimpleDocTemplate(
            output_path,
            pagesize=A4,
            rightMargin=72,
            leftMargin=72,
            topMargin=72,
            bottomMargin=18
        )
        
        story = []
        
        # Cabeçalho
        story.append(Paragraph("SAÚDE FLEX", self.styles['CustomTitle']))
        story.append(Paragraph("Relatório de Vendas", self.styles['CustomSubtitle']))
        story.append(Paragraph(f"Período: {periodo}", self.styles['SectionTitle']))
        story.append(Spacer(1, 20))
        
        # Resumo
        total_vendas = len(vendas)
        total_faturamento = sum(venda.get('total_final', 0) for venda in vendas)
        ticket_medio = total_faturamento / total_vendas if total_vendas > 0 else 0
        
        resumo_data = [
            ['Total de Vendas:', str(total_vendas)],
            ['Faturamento Total:', f"R$ {total_faturamento:.2f}"],
            ['Ticket Médio:', f"R$ {ticket_medio:.2f}"]
        ]
        
        resumo_table = Table(resumo_data, colWidths=[2*inch, 2*inch])
        resumo_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 12),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
        ]))
        
        story.append(resumo_table)
        story.append(Spacer(1, 30))
        
        # Tabela de vendas
        if vendas:
            story.append(Paragraph("Detalhamento das Vendas", self.styles['SectionTitle']))
            
            vendas_data = [['Data', 'Cliente', 'Vendedor', 'Total']]
            
            for venda in vendas:
                vendas_data.append([
                    venda.get('data_venda', 'N/A'),
                    venda.get('cliente_nome', 'N/A'),
                    venda.get('vendedor_nome', 'N/A'),
                    f"R$ {venda.get('total_final', 0):.2f}"
                ])
            
            vendas_table = Table(vendas_data, colWidths=[1.5*inch, 2*inch, 1.5*inch, 1*inch])
            vendas_table.setStyle(TableStyle([
                # Cabeçalho
                ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#f8f9fa')),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.HexColor('#495057')),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 10),
                
                # Dados
                ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 1), (-1, -1), 9),
                ('ALIGN', (-1, 1), (-1, -1), 'RIGHT'),
                
                # Bordas
                ('GRID', (0, 0), (-1, -1), 1, colors.HexColor('#dee2e6')),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
                ('TOPPADDING', (0, 0), (-1, -1), 6),
            ]))
            
            story.append(vendas_table)
        
        # Rodapé
        story.append(Spacer(1, 30))
        footer_text = f"""
        <para align="center">
        Saúde Flex - Sistema de Agendamentos e Vendas<br/>
        Relatório gerado em {datetime.now().strftime('%d/%m/%Y às %H:%M')}
        </para>
        """
        
        story.append(Paragraph(footer_text, self.styles['Normal']))
        
        # Gerar PDF
        doc.build(story)
        
        return output_path
