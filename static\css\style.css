/* <PERSON><PERSON>de <PERSON>lex - Custom Styles */

:root {
    --primary-color: #4e73df;
    --secondary-color: #858796;
    --success-color: #1cc88a;
    --info-color: #36b9cc;
    --warning-color: #f6c23e;
    --danger-color: #e74a3b;
    --light-color: #f8f9fc;
    --dark-color: #5a5c69;
}

/* Global Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fc;
    color: #5a5c69;
}

/* Navbar Customizations */
.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover {
    color: rgba(255, 255, 255, 0.8) !important;
}

/* Card Enhancements */
.card {
    border: none;
    border-radius: 0.75rem;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 0.25rem 2rem 0 rgba(58, 59, 69, 0.2);
}

.card-header {
    background-color: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
    border-radius: 0.75rem 0.75rem 0 0 !important;
}

/* Button Enhancements */
.btn {
    border-radius: 0.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

/* Form Enhancements */
.form-control {
    border-radius: 0.5rem;
    border: 1px solid #d1d3e2;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

/* Table Enhancements */
.table {
    border-radius: 0.5rem;
    overflow: hidden;
}

.table thead th {
    background-color: var(--light-color);
    border: none;
    font-weight: 600;
    color: var(--dark-color);
    padding: 1rem 0.75rem;
}

.table tbody tr {
    transition: all 0.3s ease;
}

.table tbody tr:hover {
    background-color: rgba(78, 115, 223, 0.05);
}

.table tbody td {
    padding: 1rem 0.75rem;
    border-top: 1px solid #e3e6f0;
    vertical-align: middle;
}

/* Badge Enhancements */
.badge {
    font-weight: 500;
    padding: 0.5rem 0.75rem;
    border-radius: 0.5rem;
}

/* Alert Enhancements */
.alert {
    border: none;
    border-radius: 0.75rem;
    border-left: 4px solid;
}

.alert-success {
    background-color: rgba(28, 200, 138, 0.1);
    border-left-color: var(--success-color);
    color: #0f5132;
}

.alert-danger {
    background-color: rgba(231, 74, 59, 0.1);
    border-left-color: var(--danger-color);
    color: #842029;
}

.alert-warning {
    background-color: rgba(246, 194, 62, 0.1);
    border-left-color: var(--warning-color);
    color: #664d03;
}

.alert-info {
    background-color: rgba(54, 185, 204, 0.1);
    border-left-color: var(--info-color);
    color: #055160;
}

/* Dashboard Cards */
.border-left-primary {
    border-left: 0.25rem solid var(--primary-color) !important;
}

.border-left-success {
    border-left: 0.25rem solid var(--success-color) !important;
}

.border-left-info {
    border-left: 0.25rem solid var(--info-color) !important;
}

.border-left-warning {
    border-left: 0.25rem solid var(--warning-color) !important;
}

.text-xs {
    font-size: 0.7rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.text-gray-800 {
    color: var(--dark-color) !important;
}

.text-gray-300 {
    color: #dddfeb !important;
}

/* Loading Spinner */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Custom Utilities */
.shadow-sm {
    box-shadow: 0 0.125rem 0.25rem 0 rgba(58, 59, 69, 0.2) !important;
}

.shadow {
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
}

.shadow-lg {
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important;
}

/* Responsive Enhancements */
@media (max-width: 768px) {
    .card-body {
        padding: 1rem;
    }
    
    .table-responsive {
        border-radius: 0.5rem;
    }
    
    .btn-group .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        transform: translateX(-100%);
    }
    to {
        transform: translateX(0);
    }
}

/* Custom Components */
.stat-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 1rem;
    padding: 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 1rem 2rem rgba(102, 126, 234, 0.3);
}

.stat-card .stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stat-card .stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Product Card */
.product-card {
    border: 1px solid #e3e6f0;
    border-radius: 1rem;
    padding: 1rem;
    transition: all 0.3s ease;
    background: white;
}

.product-card:hover {
    border-color: var(--primary-color);
    box-shadow: 0 0.5rem 1rem rgba(78, 115, 223, 0.15);
    transform: translateY(-2px);
}

.product-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: 0.5rem;
    background-color: #f8f9fc;
}

/* Status Indicators */
.status-indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 0.5rem;
}

.status-active {
    background-color: var(--success-color);
}

.status-inactive {
    background-color: var(--danger-color);
}

.status-pending {
    background-color: var(--warning-color);
}

/* Footer */
footer {
    margin-top: auto;
    background-color: #f8f9fc !important;
    border-top: 1px solid #e3e6f0;
}
