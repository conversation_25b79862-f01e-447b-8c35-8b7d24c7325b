#!/usr/bin/env python3
"""
Teste simples para verificar se o sistema está funcionando
"""

try:
    print("Testando importações...")
    
    # Testar importações básicas
    import os
    import sys
    print("✓ Módulos básicos do Python")
    
    # Testar Flask
    try:
        import flask
        print("✓ Flask")
    except ImportError:
        print("❌ Flask não encontrado")
        sys.exit(1)
    
    # Testar bcrypt
    try:
        import bcrypt
        print("✓ bcrypt")
    except ImportError:
        print("❌ bcrypt não encontrado")
        sys.exit(1)
    
    # Testar reportlab
    try:
        import reportlab
        print("✓ reportlab")
    except ImportError:
        print("❌ reportlab não encontrado")
        sys.exit(1)
    
    # Testar configurações
    try:
        from config import AppConfig
        print("✓ Configurações")
        print(f"  - Database path: {AppConfig.DATABASE_PATH}")
        print(f"  - Static folder: {AppConfig.STATIC_FOLDER}")
    except ImportError as e:
        print(f"❌ Erro nas configurações: {e}")
        sys.exit(1)
    
    # Testar database
    try:
        from database import db
        print("✓ Database")
    except ImportError as e:
        print(f"❌ Erro no database: {e}")
        sys.exit(1)
    
    # Testar models
    try:
        from models import Usuario, Produto, Cliente, Agendamento, Categoria, Venda
        print("✓ Models")
    except ImportError as e:
        print(f"❌ Erro nos models: {e}")
        sys.exit(1)
    
    # Testar PDF generator
    try:
        from pdf_generator import PDFGenerator
        print("✓ PDF Generator")
    except ImportError as e:
        print(f"❌ Erro no PDF Generator: {e}")
        sys.exit(1)
    
    print("\n🎉 Todos os testes passaram!")
    print("O sistema está pronto para ser executado.")
    
    # Testar criação de usuário admin
    try:
        admin = Usuario.buscar_por_email('<EMAIL>')
        if admin:
            print(f"✓ Usuário admin encontrado: {admin.nome}")
        else:
            print("ℹ️ Usuário admin será criado na primeira execução")
    except Exception as e:
        print(f"ℹ️ Database será inicializado na primeira execução: {e}")
    
except Exception as e:
    print(f"❌ Erro inesperado: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
