# 🏥 Saúde Flex - Sistema de Agendamentos

Sistema completo de agendamentos para empresas de produtos de saúde e bem-estar, desenvolvido em Python com Flask e SQLite.

## 📋 Funcionalidades

### 👥 Gestão de Usuários
- **3 tipos de usuário**: Admin, Gerente e Vendedor
- **Controle de permissões** baseado no tipo de usuário
- **Autenticação segura** com senhas criptografadas (bcrypt)
- **Gerenciamento completo** de usuários (apenas para admins)

### 📦 Gestão de Produtos
- **Cadastro completo** com cálculo automático de preço de venda
- **Controle de estoque** com alertas de estoque baixo
- **Categorização** de produtos
- **Upload de fotos** dos produtos
- **Cálculo de lucro** em valor fixo ou percentual

### 👤 Gestão de Clientes
- **Cadastro completo** com dados pessoais e endereço
- **Busca automática** de endereço por CEP (ViaCEP)
- **Histórico** de agendamentos e vendas
- **Filtros avançados** para busca

### 📅 Sistema de Agendamentos
- **Agendamento de visitas** com data/hora
- **Controle de status** (Agendado, Confirmado, Realizado, Cancelado)
- **Verificação de conflitos** de horário
- **Filtros por período**, vendedor e status
- **Interface intuitiva** com calendário

### 💰 Sistema de Vendas
- **Carrinho de compras** interativo
- **Descontos individuais** por produto
- **Desconto total** na venda
- **Geração automática** de comprovantes PDF
- **Controle de estoque** automático
- **Cálculo em tempo real** dos valores

### 📊 Relatórios Estratégicos
1. **Estoque Baixo**: Produtos abaixo do estoque mínimo
2. **Vendas por Período**: Análise financeira por data
3. **Produtos Mais Vendidos**: Ranking de produtos
4. **Vendas por Vendedor**: Performance da equipe
5. **Relatório de Lucro**: Análise de lucratividade
6. **Agendamentos**: Controle de visitas agendadas

## 🛠️ Tecnologias Utilizadas

### Backend
- **Python 3.13**
- **Flask 2.3.3** - Framework web
- **SQLite** - Banco de dados
- **bcrypt** - Criptografia de senhas
- **ReportLab** - Geração de PDFs

### Frontend
- **HTML5/CSS3**
- **Bootstrap 5.3** - Framework CSS
- **JavaScript ES6**
- **Font Awesome** - Ícones
- **Chart.js** - Gráficos
- **jQuery** - Manipulação DOM

## 🚀 Instalação e Execução

### Pré-requisitos
- Python 3.8 ou superior
- pip (gerenciador de pacotes Python)

### Passo a passo

1. **Clone o repositório**
```bash
git clone <url-do-repositorio>
cd saude-flex
```

2. **Instale as dependências**
```bash
pip install -r requirements.txt
```

3. **Execute a aplicação**
```bash
python app.py
```

4. **Acesse o sistema**
- Abra o navegador em: `http://localhost:5000`
- **Login padrão**:
  - Email: `<EMAIL>`
  - Senha: `admin123`

## 📁 Estrutura do Projeto

```
saude-flex/
├── app.py                 # Aplicação principal Flask
├── database.py           # Configuração do banco de dados
├── models.py             # Modelos de dados
├── requirements.txt      # Dependências Python
├── saude_flex.db        # Banco de dados SQLite
├── static/              # Arquivos estáticos
│   ├── css/
│   │   └── style.css    # Estilos customizados
│   ├── js/
│   │   └── main.js      # JavaScript principal
│   └── img/             # Imagens e uploads
└── templates/           # Templates HTML
    ├── base.html        # Template base
    ├── login.html       # Página de login
    ├── dashboard.html   # Dashboard principal
    ├── produtos/        # Templates de produtos
    ├── clientes/        # Templates de clientes
    ├── agendamentos/    # Templates de agendamentos
    ├── vendas/          # Templates de vendas
    ├── relatorios/      # Templates de relatórios
    └── usuarios/        # Templates de usuários
```

## 🔐 Tipos de Usuário e Permissões

### 👑 Administrador
- Acesso total ao sistema
- Gerenciamento de usuários
- Todos os relatórios
- Configurações do sistema

### 👔 Gerente
- Gerenciamento de produtos
- Gerenciamento de clientes
- Vendas e agendamentos
- Relatórios gerenciais

### 👤 Vendedor
- Cadastro de clientes
- Agendamentos de visitas
- Realização de vendas
- Relatórios próprios

## 🎨 Design e UX

- **Interface moderna** e responsiva
- **Cores consistentes** com a identidade visual
- **Navegação intuitiva** com breadcrumbs
- **Feedback visual** para todas as ações
- **Animações suaves** e transições
- **Compatibilidade mobile** completa

## 📈 Funcionalidades Avançadas

### Cálculo Automático de Preços
- Lucro em **valor fixo** ou **percentual**
- **Preview em tempo real** dos valores
- **Validação** de dados de entrada

### Controle de Estoque
- **Alertas automáticos** de estoque baixo
- **Atualização automática** nas vendas
- **Relatórios específicos** de estoque

### Sistema de Relatórios
- **Gráficos interativos** com Chart.js
- **Filtros avançados** por período
- **Exportação** para PDF
- **Envio por email** (planejado)

## 🔧 Configurações

### Banco de Dados
O sistema utiliza SQLite com as seguintes tabelas:
- `usuarios` - Dados dos usuários
- `produtos` - Catálogo de produtos
- `categorias` - Categorias de produtos
- `clientes` - Base de clientes
- `agendamentos` - Agendamentos de visitas
- `vendas` - Registro de vendas
- `itens_venda` - Itens das vendas
- `logs_auditoria` - Logs do sistema

### Segurança
- **Senhas criptografadas** com bcrypt
- **Validação de sessões**
- **Controle de permissões**
- **Logs de auditoria**
- **Prevenção SQL Injection**

## 🚧 Próximas Funcionalidades

- [ ] **Integração com WhatsApp** para notificações
- [ ] **Sistema de backup** automático
- [ ] **API REST** para integrações
- [ ] **App mobile** nativo
- [ ] **Relatórios em Excel**
- [ ] **Dashboard em tempo real**
- [ ] **Sistema de comissões**
- [ ] **Integração com e-commerce**

## 📞 Suporte

Para dúvidas, sugestões ou problemas:
- **Email**: <EMAIL>
- **Documentação**: Consulte os comentários no código
- **Issues**: Use o sistema de issues do repositório

## 📄 Licença

Este projeto está sob a licença MIT. Veja o arquivo `LICENSE` para mais detalhes.

---

**Desenvolvido com ❤️ para empresas de saúde e bem-estar**
