{% extends "base.html" %}

{% block title %}Detalhes da Venda #{{ venda.id }} - Saúde Flex{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-shopping-cart me-2 text-primary"></i>
                Venda #{{ venda.id }}
            </h1>
            <div>
                <a href="{{ url_for('vendas') }}" class="btn btn-outline-secondary me-2">
                    <i class="fas fa-arrow-left me-2"></i>
                    Voltar
                </a>
                <a href="{{ url_for('gerar_comprovante', venda_id=venda.id) }}" target="_blank" class="btn btn-info">
                    <i class="fas fa-print me-2"></i>
                    Comprovante
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Informações da Venda -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-info-circle me-2"></i>
                    Informações da Venda
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-sm-6">
                        <p class="mb-2">
                            <strong><i class="fas fa-calendar me-2 text-muted"></i>Data:</strong><br>
                            {{ venda.data_venda.strftime('%d/%m/%Y às %H:%M') if venda.data_venda else 'N/A' }}
                        </p>
                        <p class="mb-2">
                            <strong><i class="fas fa-user me-2 text-muted"></i>Cliente:</strong><br>
                            {{ venda.cliente_nome }}
                        </p>
                        {% if venda.cliente_telefone %}
                        <p class="mb-2">
                            <strong><i class="fas fa-phone me-2 text-muted"></i>Telefone:</strong><br>
                            {{ venda.cliente_telefone }}
                        </p>
                        {% endif %}
                    </div>
                    <div class="col-sm-6">
                        <p class="mb-2">
                            <strong><i class="fas fa-user-tie me-2 text-muted"></i>Vendedor:</strong><br>
                            {{ venda.vendedor_nome }}
                        </p>
                        {% if venda.observacoes %}
                        <p class="mb-2">
                            <strong><i class="fas fa-sticky-note me-2 text-muted"></i>Observações:</strong><br>
                            {{ venda.observacoes }}
                        </p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-success">
                    <i class="fas fa-calculator me-2"></i>
                    Resumo Financeiro
                </h6>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between mb-2">
                    <span>Subtotal:</span>
                    <span>R$ {{ "%.2f"|format(venda.subtotal) }}</span>
                </div>
                {% if venda.desconto_total > 0 %}
                <div class="d-flex justify-content-between mb-2">
                    <span>Desconto Total:</span>
                    <span class="text-warning">-R$ {{ "%.2f"|format(venda.desconto_total) }}</span>
                </div>
                {% endif %}
                <hr>
                <div class="d-flex justify-content-between">
                    <strong>Total Final:</strong>
                    <strong class="text-success h5">R$ {{ "%.2f"|format(venda.total_final) }}</strong>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Itens da Venda -->
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-list me-2"></i>
                    Itens da Venda
                </h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Produto</th>
                                <th>Quantidade</th>
                                <th>Preço Unitário</th>
                                <th>Desconto Item</th>
                                <th>Subtotal</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in venda.itens %}
                            <tr>
                                <td>
                                    <i class="fas fa-box me-2 text-muted"></i>
                                    {{ item.produto_nome }}
                                </td>
                                <td>
                                    <span class="badge bg-primary">{{ item.quantidade }}</span>
                                </td>
                                <td>R$ {{ "%.2f"|format(item.preco_unitario) }}</td>
                                <td>
                                    {% if item.desconto_item > 0 %}
                                    <span class="text-warning">-R$ {{ "%.2f"|format(item.desconto_item) }}</span>
                                    {% else %}
                                    <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <strong>R$ {{ "%.2f"|format(item.subtotal_item) }}</strong>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                        <tfoot>
                            <tr class="table-active">
                                <th colspan="4">Total dos Itens:</th>
                                <th>R$ {{ "%.2f"|format(venda.subtotal) }}</th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.card {
    border: none;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
}

.table-active {
    background-color: rgba(0, 123, 255, 0.1) !important;
}

.badge {
    font-size: 0.875em;
}
</style>
{% endblock %}
