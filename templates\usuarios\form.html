{% extends "base.html" %}

{% block title %}{{ 'Editar' if usuario else 'Novo' }} Usuário - Saúde Flex{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-{{ 'user-edit' if usuario else 'user-plus' }} me-2 text-primary"></i>
                {{ 'Editar' if usuario else 'Novo' }} Usuário
            </h1>
            <a href="{{ url_for('usuarios') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>
                Voltar
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-info-circle me-2"></i>
                    Informações do Usuário
                </h6>
            </div>
            <div class="card-body">
                <form method="POST" id="form-usuario">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="nome" class="form-label">
                                    <i class="fas fa-user me-1"></i>
                                    Nome Completo *
                                </label>
                                <input type="text" class="form-control" id="nome" name="nome" 
                                       value="{{ usuario.nome if usuario else '' }}" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="email" class="form-label">
                                    <i class="fas fa-envelope me-1"></i>
                                    Email *
                                </label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="{{ usuario.email if usuario else '' }}" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="tipo" class="form-label">
                                    <i class="fas fa-user-tag me-1"></i>
                                    Tipo de Usuário *
                                </label>
                                <select class="form-control" id="tipo" name="tipo" required onchange="atualizarPermissoes()">
                                    <option value="">Selecione o tipo</option>
                                    <option value="admin" {{ 'selected' if usuario and usuario.tipo == 'admin' else '' }}>
                                        Administrador
                                    </option>
                                    <option value="gerente" {{ 'selected' if usuario and usuario.tipo == 'gerente' else '' }}>
                                        Gerente
                                    </option>
                                    <option value="vendedor" {{ 'selected' if usuario and usuario.tipo == 'vendedor' else '' }}>
                                        Vendedor
                                    </option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="senha" class="form-label">
                                    <i class="fas fa-lock me-1"></i>
                                    Senha {{ 'Nova ' if usuario else '' }}*
                                </label>
                                <input type="password" class="form-control" id="senha" name="senha" 
                                       {{ 'required' if not usuario else '' }}>
                                {% if usuario %}
                                <small class="text-muted">Deixe em branco para manter a senha atual</small>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    {% if not usuario %}
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="confirmar_senha" class="form-label">
                                    <i class="fas fa-lock me-1"></i>
                                    Confirmar Senha *
                                </label>
                                <input type="password" class="form-control" id="confirmar_senha" name="confirmar_senha" required>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Permissões -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-shield-alt me-2"></i>
                                Permissões do Usuário
                            </h6>
                            <div id="permissoes-info" class="alert alert-info">
                                Selecione um tipo de usuário para ver as permissões.
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between mt-4">
                        <a href="{{ url_for('usuarios') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>
                            Cancelar
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            {{ 'Atualizar' if usuario else 'Cadastrar' }} Usuário
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- Preview do Usuário -->
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-eye me-2"></i>
                    Preview do Usuário
                </h6>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    <div id="preview-avatar" class="bg-primary rounded-circle d-flex align-items-center justify-content-center mx-auto" 
                         style="width: 80px; height: 80px;">
                        <i class="fas fa-user fa-2x text-white"></i>
                    </div>
                </div>
                
                <h5 id="preview-nome" class="text-center">{{ usuario.nome if usuario else 'Nome do Usuário' }}</h5>
                
                <div class="mt-3">
                    <p class="mb-2">
                        <i class="fas fa-envelope text-muted me-2"></i>
                        <span id="preview-email">{{ usuario.email if usuario else 'Email' }}</span>
                    </p>
                    <p class="mb-2">
                        <i class="fas fa-user-tag text-muted me-2"></i>
                        <span id="preview-tipo">{{ usuario.tipo.title() if usuario else 'Tipo' }}</span>
                    </p>
                    <p class="mb-2">
                        <i class="fas fa-calendar text-muted me-2"></i>
                        <span id="preview-data">{{ usuario.data_criacao if usuario else 'Data de Criação' }}</span>
                    </p>
                </div>
            </div>
        </div>

        <!-- Informações de Segurança -->
        <div class="card shadow mt-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-warning">
                    <i class="fas fa-shield-alt me-2"></i>
                    Segurança
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        <small>Senha será criptografada automaticamente</small>
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        <small>Email deve ser único no sistema</small>
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        <small>Permissões baseadas no tipo de usuário</small>
                    </li>
                    <li>
                        <i class="fas fa-check text-success me-2"></i>
                        <small>Logs de atividade são registrados</small>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Dicas -->
        <div class="card shadow mt-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-info">
                    <i class="fas fa-lightbulb me-2"></i>
                    Dicas
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li class="mb-2">
                        <i class="fas fa-info text-info me-2"></i>
                        <small><strong>Admin:</strong> Acesso total ao sistema</small>
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-info text-info me-2"></i>
                        <small><strong>Gerente:</strong> Gerencia produtos, vendas e relatórios</small>
                    </li>
                    <li>
                        <i class="fas fa-info text-info me-2"></i>
                        <small><strong>Vendedor:</strong> Apenas vendas e agendamentos</small>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Atualizar preview em tempo real
document.getElementById('nome').addEventListener('input', function() {
    document.getElementById('preview-nome').textContent = this.value || 'Nome do Usuário';
});

document.getElementById('email').addEventListener('input', function() {
    document.getElementById('preview-email').textContent = this.value || 'Email';
});

document.getElementById('tipo').addEventListener('change', function() {
    const tipo = this.value;
    const previewTipo = document.getElementById('preview-tipo');
    const previewAvatar = document.getElementById('preview-avatar');
    
    if (tipo) {
        previewTipo.textContent = tipo.charAt(0).toUpperCase() + tipo.slice(1);
        
        // Atualizar cor do avatar
        previewAvatar.className = previewAvatar.className.replace(/bg-\w+/, '');
        if (tipo === 'admin') {
            previewAvatar.classList.add('bg-primary');
            previewAvatar.innerHTML = '<i class="fas fa-user-shield fa-2x text-white"></i>';
        } else if (tipo === 'gerente') {
            previewAvatar.classList.add('bg-info');
            previewAvatar.innerHTML = '<i class="fas fa-user-tie fa-2x text-white"></i>';
        } else if (tipo === 'vendedor') {
            previewAvatar.classList.add('bg-warning');
            previewAvatar.innerHTML = '<i class="fas fa-user fa-2x text-white"></i>';
        }
    } else {
        previewTipo.textContent = 'Tipo';
        previewAvatar.className = 'bg-primary rounded-circle d-flex align-items-center justify-content-center mx-auto';
        previewAvatar.innerHTML = '<i class="fas fa-user fa-2x text-white"></i>';
    }
});

// Atualizar informações de permissões
function atualizarPermissoes() {
    const tipo = document.getElementById('tipo').value;
    const permissoesInfo = document.getElementById('permissoes-info');
    
    const permissoes = {
        admin: {
            classe: 'alert-primary',
            titulo: 'Administrador - Acesso Total',
            lista: [
                'Gerenciar usuários (criar, editar, excluir)',
                'Acesso a todos os relatórios',
                'Gerenciar produtos e categorias',
                'Gerenciar clientes e agendamentos',
                'Realizar e gerenciar vendas',
                'Configurações do sistema'
            ]
        },
        gerente: {
            classe: 'alert-info',
            titulo: 'Gerente - Acesso Gerencial',
            lista: [
                'Gerenciar produtos e categorias',
                'Gerenciar clientes e agendamentos',
                'Realizar e gerenciar vendas',
                'Acesso a relatórios gerenciais',
                'Visualizar dados de vendedores'
            ]
        },
        vendedor: {
            classe: 'alert-warning',
            titulo: 'Vendedor - Acesso Limitado',
            lista: [
                'Cadastrar novos clientes',
                'Criar agendamentos de visitas',
                'Realizar vendas',
                'Visualizar próprios dados de venda',
                'Atualizar informações de clientes'
            ]
        }
    };
    
    if (tipo && permissoes[tipo]) {
        const perm = permissoes[tipo];
        permissoesInfo.className = `alert ${perm.classe}`;
        permissoesInfo.innerHTML = `
            <strong>${perm.titulo}</strong>
            <ul class="mt-2 mb-0">
                ${perm.lista.map(item => `<li>${item}</li>`).join('')}
            </ul>
        `;
    } else {
        permissoesInfo.className = 'alert alert-info';
        permissoesInfo.innerHTML = 'Selecione um tipo de usuário para ver as permissões.';
    }
}

// Validação do formulário
document.getElementById('form-usuario').addEventListener('submit', function(e) {
    const senha = document.getElementById('senha').value;
    const confirmarSenha = document.getElementById('confirmar_senha');
    
    // Validar confirmação de senha (apenas para novos usuários)
    if (confirmarSenha && senha !== confirmarSenha.value) {
        e.preventDefault();
        SaudeFlex.notify.error('As senhas não coincidem.');
        confirmarSenha.focus();
        return;
    }
    
    // Validar força da senha
    if (senha && senha.length < 6) {
        e.preventDefault();
        SaudeFlex.notify.error('A senha deve ter pelo menos 6 caracteres.');
        document.getElementById('senha').focus();
        return;
    }
});

// Inicialização
document.addEventListener('DOMContentLoaded', function() {
    atualizarPermissoes();
    
    // Definir data atual no preview se for novo usuário
    {% if not usuario %}
    document.getElementById('preview-data').textContent = new Date().toLocaleDateString('pt-BR');
    {% endif %}
});
</script>
{% endblock %}
