{% extends "base.html" %}

{% block title %}Agendamentos - Saúde Flex{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-calendar-alt me-2 text-primary"></i>
                Agendamentos
            </h1>
            <a href="{{ url_for('agendamento_novo') }}" class="btn btn-primary">
                <i class="fas fa-calendar-plus me-2"></i>
                Novo Agendamento
            </a>
        </div>
    </div>
</div>

<!-- Filtros -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="filtro-data-inicio">Data Início</label>
                            <input type="date" class="form-control" id="filtro-data-inicio">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="filtro-data-fim">Data Fim</label>
                            <input type="date" class="form-control" id="filtro-data-fim">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="filtro-status">Status</label>
                            <select class="form-control" id="filtro-status">
                                <option value="">Todos os status</option>
                                <option value="agendado">Agendado</option>
                                <option value="confirmado">Confirmado</option>
                                <option value="realizado">Realizado</option>
                                <option value="cancelado">Cancelado</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="filtro-vendedor">Vendedor</label>
                            <select class="form-control" id="filtro-vendedor">
                                <option value="">Todos os vendedores</option>
                                <!-- Será preenchido via JavaScript -->
                            </select>
                        </div>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-md-6">
                        <button type="button" class="btn btn-info" onclick="filtrarAgendamentos()">
                            <i class="fas fa-search me-2"></i>
                            Filtrar
                        </button>
                        <button type="button" class="btn btn-outline-secondary ms-2" onclick="limparFiltros()">
                            <i class="fas fa-eraser me-2"></i>
                            Limpar
                        </button>
                    </div>
                    <div class="col-md-6 text-end">
                        <button type="button" class="btn btn-outline-primary" onclick="visualizarCalendario()">
                            <i class="fas fa-calendar me-2"></i>
                            Visualizar Calendário
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Lista de Agendamentos -->
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-list me-2"></i>
                    Lista de Agendamentos ({{ agendamentos|length }})
                </h6>
            </div>
            <div class="card-body">
                {% if agendamentos %}
                <div class="table-responsive">
                    <table class="table table-hover" id="tabela-agendamentos">
                        <thead>
                            <tr>
                                <th>Data/Hora</th>
                                <th>Cliente</th>
                                <th>Produto</th>
                                <th>Vendedor</th>
                                <th>Status</th>
                                <th>Ações</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for agendamento in agendamentos %}
                            <tr data-status="{{ agendamento.status }}" data-vendedor="{{ agendamento.vendedor_id }}">
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="bg-primary rounded me-3 d-flex align-items-center justify-content-center" 
                                             style="width: 40px; height: 40px;">
                                            <i class="fas fa-calendar text-white"></i>
                                        </div>
                                        <div>
                                            <strong>{{ agendamento.data_agendamento }}</strong>
                                            <br><small class="text-muted">{{ agendamento.data_agendamento }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <strong>{{ agendamento.cliente_nome }}</strong>
                                        <br><small class="text-muted">
                                            <i class="fas fa-phone me-1"></i>
                                            {{ agendamento.cliente_telefone }}
                                        </small>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-info">{{ agendamento.produto_nome }}</span>
                                </td>
                                <td>
                                    <i class="fas fa-user-tie me-2 text-muted"></i>
                                    {{ agendamento.vendedor_nome }}
                                </td>
                                <td>
                                    <span class="badge bg-{{ 
                                        'success' if agendamento.status == 'realizado' 
                                        else 'primary' if agendamento.status == 'confirmado'
                                        else 'warning' if agendamento.status == 'agendado'
                                        else 'danger' 
                                    }}">
                                        {{ agendamento.status.title() }}
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-sm btn-outline-primary" 
                                                onclick="verAgendamento({{ agendamento.id }})" title="Ver Detalhes">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        {% if agendamento.status in ['agendado', 'confirmado'] %}
                                        <button type="button" class="btn btn-sm btn-outline-warning" 
                                                onclick="editarAgendamento({{ agendamento.id }})" title="Editar">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-sm btn-outline-success dropdown-toggle" 
                                                    data-bs-toggle="dropdown" title="Alterar Status">
                                                <i class="fas fa-check"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                {% if agendamento.status == 'agendado' %}
                                                <li><a class="dropdown-item" href="#" onclick="alterarStatus({{ agendamento.id }}, 'confirmado')">
                                                    <i class="fas fa-check-circle me-2"></i>Confirmar
                                                </a></li>
                                                {% endif %}
                                                <li><a class="dropdown-item" href="#" onclick="alterarStatus({{ agendamento.id }}, 'realizado')">
                                                    <i class="fas fa-check-double me-2"></i>Marcar como Realizado
                                                </a></li>
                                                <li><hr class="dropdown-divider"></li>
                                                <li><a class="dropdown-item text-danger" href="#" onclick="alterarStatus({{ agendamento.id }}, 'cancelado')">
                                                    <i class="fas fa-times me-2"></i>Cancelar
                                                </a></li>
                                            </ul>
                                        </div>
                                        {% endif %}
                                        {% if usuario_logado.tipo in ['admin', 'gerente'] %}
                                        <button type="button" class="btn btn-sm btn-outline-danger" 
                                                onclick="excluirAgendamento({{ agendamento.id }})" title="Excluir">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">Nenhum agendamento encontrado</h5>
                    <p class="text-muted">Comece criando seu primeiro agendamento.</p>
                    <a href="{{ url_for('agendamento_novo') }}" class="btn btn-primary">
                        <i class="fas fa-calendar-plus me-2"></i>
                        Criar Primeiro Agendamento
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Modal de Detalhes do Agendamento -->
<div class="modal fade" id="modalDetalhesAgendamento" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-calendar-alt me-2"></i>
                    Detalhes do Agendamento
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="conteudo-detalhes-agendamento">
                <!-- Conteúdo será carregado via JavaScript -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
                <button type="button" class="btn btn-warning" onclick="editarAgendamentoModal()">
                    <i class="fas fa-edit me-2"></i>
                    Editar
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.css" rel="stylesheet">
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/locales/pt-br.global.min.js"></script>

<script>
let agendamentoAtual = null;

// Filtros
function filtrarAgendamentos() {
    const dataInicio = document.getElementById('filtro-data-inicio').value;
    const dataFim = document.getElementById('filtro-data-fim').value;
    const status = document.getElementById('filtro-status').value;
    const vendedor = document.getElementById('filtro-vendedor').value;
    
    const linhas = document.querySelectorAll('#tabela-agendamentos tbody tr');
    
    linhas.forEach(linha => {
        let mostrar = true;
        
        // Filtro por status
        if (status && linha.dataset.status !== status) {
            mostrar = false;
        }
        
        // Filtro por vendedor
        if (vendedor && linha.dataset.vendedor !== vendedor) {
            mostrar = false;
        }
        
        // TODO: Implementar filtros de data
        
        linha.style.display = mostrar ? '' : 'none';
    });
}

function limparFiltros() {
    document.getElementById('filtro-data-inicio').value = '';
    document.getElementById('filtro-data-fim').value = '';
    document.getElementById('filtro-status').value = '';
    document.getElementById('filtro-vendedor').value = '';
    filtrarAgendamentos();
}

function verAgendamento(id) {
    agendamentoAtual = id;
    
    // Simular carregamento de dados do agendamento
    const conteudo = `
        <div class="row">
            <div class="col-md-6">
                <h6><i class="fas fa-calendar me-2"></i>Informações do Agendamento</h6>
                <p><strong>Data/Hora:</strong> 15/06/2024 14:30</p>
                <p><strong>Status:</strong> <span class="badge bg-primary">Confirmado</span></p>
                <p><strong>Observações:</strong> Cliente interessado no produto X</p>
            </div>
            <div class="col-md-6">
                <h6><i class="fas fa-user me-2"></i>Cliente</h6>
                <p><strong>Nome:</strong> Cliente Exemplo</p>
                <p><strong>Telefone:</strong> (11) 99999-9999</p>
                <p><strong>Endereço:</strong> Rua Exemplo, 123 - Centro</p>
            </div>
        </div>
        <hr>
        <div class="row">
            <div class="col-md-6">
                <h6><i class="fas fa-box me-2"></i>Produto de Interesse</h6>
                <p><strong>Nome:</strong> Produto Exemplo</p>
                <p><strong>Preço:</strong> R$ 150,00</p>
            </div>
            <div class="col-md-6">
                <h6><i class="fas fa-user-tie me-2"></i>Vendedor Responsável</h6>
                <p><strong>Nome:</strong> Vendedor Exemplo</p>
                <p><strong>Email:</strong> <EMAIL></p>
            </div>
        </div>
    `;
    
    document.getElementById('conteudo-detalhes-agendamento').innerHTML = conteudo;
    
    const modal = new bootstrap.Modal(document.getElementById('modalDetalhesAgendamento'));
    modal.show();
}

function editarAgendamento(id) {
    window.location.href = '/agendamentos/editar/' + id;
}

function editarAgendamentoModal() {
    if (agendamentoAtual) {
        editarAgendamento(agendamentoAtual);
    }
}

function alterarStatus(id, novoStatus) {
    const statusTexto = {
        'confirmado': 'confirmar',
        'realizado': 'marcar como realizado',
        'cancelado': 'cancelar'
    };
    
    SaudeFlex.modal.confirm(
        `Tem certeza que deseja ${statusTexto[novoStatus]} este agendamento?`,
        function() {
            // Implementar alteração de status
            SaudeFlex.notify.success(`Agendamento ${statusTexto[novoStatus]} com sucesso!`);
            // Recarregar página ou atualizar linha da tabela
            location.reload();
        }
    );
}

function excluirAgendamento(id) {
    SaudeFlex.modal.confirm(
        'Tem certeza que deseja excluir este agendamento? Esta ação não pode ser desfeita.',
        function() {
            // Implementar exclusão
            SaudeFlex.notify.success('Agendamento excluído com sucesso!');
            // Recarregar página ou remover linha da tabela
            location.reload();
        }
    );
}

function visualizarCalendario() {
    // Implementar visualização em calendário
    SaudeFlex.notify.info('Visualização em calendário será implementada em breve!');
}

// Inicialização
document.addEventListener('DOMContentLoaded', function() {
    // Definir data padrão para filtros (últimos 30 dias)
    const hoje = new Date();
    const trintaDiasAtras = new Date(hoje.getTime() - (30 * 24 * 60 * 60 * 1000));
    
    document.getElementById('filtro-data-inicio').value = trintaDiasAtras.toISOString().split('T')[0];
    document.getElementById('filtro-data-fim').value = hoje.toISOString().split('T')[0];
});
</script>
{% endblock %}
