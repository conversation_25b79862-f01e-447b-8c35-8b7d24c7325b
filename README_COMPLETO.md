# 🏥 Saúde Flex - Sistema Completo de Agendamentos e Vendas

Sistema avançado de agendamentos e vendas para empresas de produtos de saúde e bem-estar, **preparado para compilação em executável (.exe)**.

## ✨ Funcionalidades Principais

### 👥 **Gerenciamento de Usuários**
- **Admin:** Acesso total ao sistema
- **Gerente:** Gerencia produtos, vendas, agendamentos e clientes
- **Vendedor:** Vendas, agendamentos e cadastro de clientes

### 📦 **Gestão de Produtos**
- Cadastro completo com categorias
- **Cálculo automático de preço** (lucro em R$ ou %)
- Controle de estoque com alertas
- Upload de fotos dos produtos

### 👤 **Cadastro de Clientes**
- Dados pessoais e endereço completo
- Histórico de compras e agendamentos
- Validação automática de dados

### 📅 **Sistema de Agendamentos**
- Interface com calendário interativo
- Validação de conflitos de horário
- Filtros por data, cliente, produto ou vendedor
- Notificações automáticas

### 💰 **Sistema de Vendas Avançado**
- **Carrinho de compras** com múltiplos produtos
- **Descontos individuais** por produto
- **Desconto total** na venda
- **Controle automático de estoque**
- **Geração de comprovantes PDF**

### 📊 **Relatórios Estratégicos**
- **Vendas por período** com gráficos
- **Produtos mais vendidos**
- **Desempenho por vendedor**
- **Relatório de lucro** detalhado
- **Produtos com estoque baixo**
- **Exportação em PDF**

### 🔒 **Sistema de Backup**
- Backup automático do banco de dados
- Backup completo do sistema
- Restauração de backups
- Limpeza automática de backups antigos

## 🚀 Tecnologias Utilizadas

- **Backend:** Python 3.x + Flask
- **Banco de Dados:** SQLite (portável)
- **Frontend:** HTML5 + Bootstrap 5 + JavaScript
- **PDF:** ReportLab para comprovantes
- **Autenticação:** bcrypt para segurança
- **Compilação:** PyInstaller para EXE

## 📋 Instalação e Execução

### 1. **Pré-requisitos**
```bash
Python 3.8+ instalado
```

### 2. **Instalação das Dependências**
```bash
pip install Flask bcrypt Werkzeug Jinja2 reportlab python-dateutil
```

### 3. **Execução do Sistema**
```bash
python app.py
```

### 4. **Acesso ao Sistema**
- **URL:** http://localhost:5000
- **Login:** <EMAIL>
- **Senha:** admin123

## 🔧 Compilação para EXE

### 1. **Instalar PyInstaller**
```bash
pip install pyinstaller
```

### 2. **Compilar o Sistema**
```bash
python build_exe.py
```

### 3. **Distribuição**
O executável será criado na pasta `SaudeFlex_Distribuicao/` com todos os arquivos necessários.

## 📁 Estrutura do Projeto

```
Saude Flex - AUGMENT (Python)/
├── 📄 app.py                    # Aplicação principal Flask
├── ⚙️ config.py                 # Configurações para dev/EXE
├── 🗄️ database.py               # Configuração do banco SQLite
├── 📊 models.py                 # Modelos de dados
├── 📄 pdf_generator.py          # Gerador de PDF
├── 💾 backup_system.py          # Sistema de backup
├── 🔨 build_exe.py              # Script para compilar EXE
├── 📋 requirements.txt          # Dependências
├── 🧪 test_app.py              # Teste do sistema
├── 📁 templates/               # Templates HTML
├── 📁 static/                  # Recursos estáticos
└── 🗄️ saude_flex.db            # Banco de dados SQLite
```

## 🎯 Funcionalidades Detalhadas

### 💰 **Sistema de Vendas**
- ✅ Seleção de cliente
- ✅ Adição de múltiplos produtos
- ✅ Controle de quantidade
- ✅ Desconto por item
- ✅ Desconto total da venda
- ✅ Cálculo automático de totais
- ✅ Validação de estoque
- ✅ Atualização automática de estoque
- ✅ Geração de comprovante PDF

### 📊 **Relatórios Implementados**
- ✅ **Estoque Baixo:** Produtos abaixo do mínimo
- ✅ **Vendas por Período:** Com filtros e gráficos
- ✅ **Produtos Mais Vendidos:** Ranking de produtos
- ✅ **Vendas por Vendedor:** Desempenho da equipe
- ✅ **Relatório de Lucro:** Análise de margem
- ✅ **Dashboard:** Estatísticas em tempo real

### 🔒 **Segurança**
- ✅ Senhas criptografadas com bcrypt
- ✅ Controle de sessões
- ✅ Validação de permissões
- ✅ Prevenção contra SQL injection
- ✅ Logs de auditoria

## 🎨 **Características Especiais para EXE**

### 📁 **Portabilidade**
- ✅ Caminhos relativos configurados
- ✅ Banco SQLite portável
- ✅ Recursos empacotados
- ✅ Configuração automática

### 🔧 **Configuração Inteligente**
- ✅ Detecta se está rodando como EXE
- ✅ Ajusta caminhos automaticamente
- ✅ Cria pastas necessárias
- ✅ Backup integrado

## 📈 **Status do Projeto**

### ✅ **Implementado (100%)**
- [x] Sistema de autenticação
- [x] Cadastro de produtos
- [x] Cadastro de clientes
- [x] Sistema de agendamentos
- [x] Sistema de vendas completo
- [x] Geração de PDF
- [x] Relatórios funcionais
- [x] Sistema de backup
- [x] Interface responsiva
- [x] Preparação para EXE

### 🎯 **Pronto para Uso**
O sistema está **100% funcional** e pronto para:
- ✅ Uso em produção
- ✅ Compilação em EXE
- ✅ Distribuição
- ✅ Customização

## 🆘 **Suporte e Documentação**

### 🔧 **Solução de Problemas**
1. **Erro de dependências:** Execute `pip install -r requirements.txt`
2. **Erro de banco:** Delete `saude_flex.db` para recriar
3. **Erro de compilação:** Verifique se PyInstaller está instalado

### 📞 **Primeiro Uso**
1. Execute o sistema
2. Faça login com as credenciais padrão
3. **Altere a senha do administrador**
4. Cadastre usuários, produtos e categorias
5. Configure o sistema conforme necessário

## 📄 **Licença**

MIT License - Livre para uso comercial e pessoal.

---

**🎉 Sistema Saúde Flex - Desenvolvido com foco em qualidade, performance e facilidade de uso!**
