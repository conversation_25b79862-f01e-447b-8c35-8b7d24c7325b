import os
import sys
from pathlib import Path

class Config:
    """Configurações do sistema adaptadas para desenvolvimento e EXE"""
    
    @staticmethod
    def get_base_path():
        """Retorna o caminho base da aplicação"""
        if getattr(sys, 'frozen', False):
            # Executando como EXE
            return Path(sys.executable).parent
        else:
            # Executando em desenvolvimento
            return Path(__file__).parent
    
    @staticmethod
    def get_database_path():
        """Retorna o caminho do banco de dados"""
        base_path = Config.get_base_path()
        return str(base_path / 'saude_flex.db')
    
    @staticmethod
    def get_backup_path():
        """Retorna o caminho para backups"""
        base_path = Config.get_base_path()
        backup_dir = base_path / 'backups'
        backup_dir.mkdir(exist_ok=True)
        return str(backup_dir)
    
    @staticmethod
    def get_upload_path():
        """Retorna o caminho para uploads"""
        base_path = Config.get_base_path()
        upload_dir = base_path / 'static' / 'img' / 'produtos'
        upload_dir.mkdir(parents=True, exist_ok=True)
        return str(upload_dir)
    
    @staticmethod
    def get_static_path():
        """Retorna o caminho para arquivos estáticos"""
        base_path = Config.get_base_path()
        static_dir = base_path / 'static'
        return str(static_dir)
    
    @staticmethod
    def get_templates_path():
        """Retorna o caminho para templates"""
        base_path = Config.get_base_path()
        templates_dir = base_path / 'templates'
        return str(templates_dir)

# Configurações da aplicação
class AppConfig:
    SECRET_KEY = 'saude_flex_secret_key_2024'
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB max
    
    # Configurações do Flask adaptadas para EXE
    STATIC_FOLDER = Config.get_static_path()
    TEMPLATE_FOLDER = Config.get_templates_path()
    UPLOAD_FOLDER = Config.get_upload_path()
    
    # Configurações do banco
    DATABASE_PATH = Config.get_database_path()
    BACKUP_PATH = Config.get_backup_path()
    
    # Configurações de desenvolvimento
    DEBUG = not getattr(sys, 'frozen', False)  # Debug apenas em desenvolvimento
    HOST = '127.0.0.1'  # Localhost para segurança
    PORT = 5000
