<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Comprovante de Venda #{{ venda.id }} - Saúde Flex</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
        }
        
        .comprovante {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            border-bottom: 2px solid #007bff;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        
        .logo {
            font-size: 28px;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 5px;
        }
        
        .subtitle {
            color: #6c757d;
            font-size: 14px;
        }
        
        .info-section {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
        }
        
        .info-box {
            flex: 1;
            margin-right: 20px;
        }
        
        .info-box:last-child {
            margin-right: 0;
        }
        
        .info-title {
            font-weight: bold;
            color: #495057;
            margin-bottom: 10px;
            border-bottom: 1px solid #dee2e6;
            padding-bottom: 5px;
        }
        
        .info-content {
            color: #6c757d;
            line-height: 1.6;
        }
        
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }
        
        .items-table th,
        .items-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }
        
        .items-table th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #495057;
        }
        
        .items-table .text-right {
            text-align: right;
        }
        
        .totals {
            float: right;
            width: 300px;
            margin-bottom: 30px;
        }
        
        .total-row {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #dee2e6;
        }
        
        .total-row.final {
            font-weight: bold;
            font-size: 18px;
            color: #28a745;
            border-bottom: 2px solid #28a745;
            margin-top: 10px;
        }
        
        .footer {
            clear: both;
            text-align: center;
            padding-top: 30px;
            border-top: 1px solid #dee2e6;
            color: #6c757d;
            font-size: 12px;
        }
        
        .signature-area {
            margin-top: 50px;
            display: flex;
            justify-content: space-between;
        }
        
        .signature-box {
            width: 200px;
            text-align: center;
        }
        
        .signature-line {
            border-top: 1px solid #000;
            margin-bottom: 5px;
        }
        
        @media print {
            body {
                background-color: white;
                padding: 0;
            }
            
            .comprovante {
                box-shadow: none;
                margin: 0;
                padding: 20px;
            }
            
            .no-print {
                display: none;
            }
        }
        
        .print-button {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .print-button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <button class="print-button no-print" onclick="window.print()">
        🖨️ Imprimir
    </button>
    
    <div class="comprovante">
        <!-- Cabeçalho -->
        <div class="header">
            <div class="logo">SAÚDE FLEX</div>
            <div class="subtitle">Sistema de Agendamentos e Vendas</div>
            <div style="margin-top: 15px; font-size: 18px; font-weight: bold;">
                COMPROVANTE DE VENDA #{{ venda.id }}
            </div>
        </div>
        
        <!-- Informações da Venda -->
        <div class="info-section">
            <div class="info-box">
                <div class="info-title">📅 Dados da Venda</div>
                <div class="info-content">
                    <strong>Data:</strong> {{ venda.data_venda.strftime('%d/%m/%Y às %H:%M') if venda.data_venda else 'N/A' }}<br>
                    <strong>Vendedor:</strong> {{ venda.vendedor_nome }}<br>
                    {% if venda.observacoes %}
                    <strong>Observações:</strong> {{ venda.observacoes }}
                    {% endif %}
                </div>
            </div>
            
            <div class="info-box">
                <div class="info-title">👤 Cliente</div>
                <div class="info-content">
                    <strong>Nome:</strong> {{ venda.cliente_nome }}<br>
                    {% if venda.cliente_telefone %}
                    <strong>Telefone:</strong> {{ venda.cliente_telefone }}<br>
                    {% endif %}
                    {% if venda.cliente_email %}
                    <strong>Email:</strong> {{ venda.cliente_email }}
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- Itens da Venda -->
        <table class="items-table">
            <thead>
                <tr>
                    <th>Produto</th>
                    <th class="text-right">Qtd</th>
                    <th class="text-right">Preço Unit.</th>
                    <th class="text-right">Desconto</th>
                    <th class="text-right">Subtotal</th>
                </tr>
            </thead>
            <tbody>
                {% for item in venda.itens %}
                <tr>
                    <td>{{ item.produto_nome }}</td>
                    <td class="text-right">{{ item.quantidade }}</td>
                    <td class="text-right">R$ {{ "%.2f"|format(item.preco_unitario) }}</td>
                    <td class="text-right">
                        {% if item.desconto_item > 0 %}
                        -R$ {{ "%.2f"|format(item.desconto_item) }}
                        {% else %}
                        -
                        {% endif %}
                    </td>
                    <td class="text-right">R$ {{ "%.2f"|format(item.subtotal_item) }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        
        <!-- Totais -->
        <div class="totals">
            <div class="total-row">
                <span>Subtotal:</span>
                <span>R$ {{ "%.2f"|format(venda.subtotal) }}</span>
            </div>
            {% if venda.desconto_total > 0 %}
            <div class="total-row">
                <span>Desconto Total:</span>
                <span>-R$ {{ "%.2f"|format(venda.desconto_total) }}</span>
            </div>
            {% endif %}
            <div class="total-row final">
                <span>TOTAL FINAL:</span>
                <span>R$ {{ "%.2f"|format(venda.total_final) }}</span>
            </div>
        </div>
        
        <!-- Área de Assinaturas -->
        <div class="signature-area">
            <div class="signature-box">
                <div class="signature-line"></div>
                <div>Assinatura do Cliente</div>
            </div>
            <div class="signature-box">
                <div class="signature-line"></div>
                <div>Assinatura do Vendedor</div>
            </div>
        </div>
        
        <!-- Rodapé -->
        <div class="footer">
            <p>Este documento comprova a realização da venda descrita acima.</p>
            <p>Saúde Flex - Sistema de Agendamentos e Vendas</p>
        </div>
    </div>
    
    <script>
        // Auto-imprimir se solicitado via parâmetro
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.get('print') === 'true') {
            window.onload = function() {
                setTimeout(function() {
                    window.print();
                }, 500);
            };
        }
    </script>
</body>
</html>
