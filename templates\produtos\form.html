{% extends "base.html" %}

{% block title %}{{ 'Editar' if produto else 'Novo' }} Produto - Saúde Flex{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-{{ 'edit' if produto else 'plus' }} me-2 text-primary"></i>
                {{ 'Editar' if produto else 'Novo' }} Produto
            </h1>
            <a href="{{ url_for('produtos') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>
                Voltar
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-info-circle me-2"></i>
                    Informações do Produto
                </h6>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data" id="form-produto">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="form-group mb-3">
                                <label for="nome" class="form-label">
                                    <i class="fas fa-tag me-1"></i>
                                    Nome do Produto *
                                </label>
                                <input type="text" class="form-control" id="nome" name="nome" 
                                       value="{{ produto.nome if produto else '' }}" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group mb-3">
                                <label for="categoria_id" class="form-label">
                                    <i class="fas fa-folder me-1"></i>
                                    Categoria
                                </label>
                                <select class="form-control" id="categoria_id" name="categoria_id">
                                    <option value="">Selecione uma categoria</option>
                                    {% for categoria in categorias %}
                                    <option value="{{ categoria.id }}" 
                                            {{ 'selected' if produto and produto.categoria_id == categoria.id else '' }}>
                                        {{ categoria.nome }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="form-group mb-3">
                        <label for="descricao" class="form-label">
                            <i class="fas fa-align-left me-1"></i>
                            Descrição
                        </label>
                        <textarea class="form-control" id="descricao" name="descricao" rows="3" 
                                  placeholder="Descrição detalhada do produto...">{{ produto.descricao if produto else '' }}</textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group mb-3">
                                <label for="preco_custo" class="form-label">
                                    <i class="fas fa-dollar-sign me-1"></i>
                                    Preço de Custo *
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text">R$</span>
                                    <input type="number" class="form-control" id="preco_custo" name="preco_custo" 
                                           step="0.01" min="0" value="{{ produto.preco_custo if produto else '' }}" 
                                           required onchange="calcularPrecoVenda()">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group mb-3">
                                <label for="lucro_desejado" class="form-label">
                                    <i class="fas fa-chart-line me-1"></i>
                                    Lucro Desejado *
                                </label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="lucro_desejado" name="lucro_desejado" 
                                           step="0.01" min="0" value="{{ produto.lucro_desejado if produto else '' }}" 
                                           required onchange="calcularPrecoVenda()">
                                    <select class="form-select" id="tipo_lucro" name="tipo_lucro" onchange="calcularPrecoVenda()">
                                        <option value="percentual" {{ 'selected' if produto and produto.tipo_lucro == 'percentual' else '' }}>%</option>
                                        <option value="valor" {{ 'selected' if produto and produto.tipo_lucro == 'valor' else '' }}>R$</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group mb-3">
                                <label for="preco_venda" class="form-label">
                                    <i class="fas fa-money-bill-wave me-1"></i>
                                    Preço de Venda
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text">R$</span>
                                    <input type="number" class="form-control bg-light" id="preco_venda" 
                                           step="0.01" readonly value="{{ produto.preco_venda if produto else '' }}">
                                </div>
                                <small class="text-muted">Calculado automaticamente</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group mb-3">
                                <label class="form-label">&nbsp;</label>
                                <button type="button" class="btn btn-info w-100" onclick="calcularPrecoVenda()">
                                    <i class="fas fa-calculator me-2"></i>
                                    Calcular Preço
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="estoque_atual" class="form-label">
                                    <i class="fas fa-boxes me-1"></i>
                                    Estoque Atual *
                                </label>
                                <input type="number" class="form-control" id="estoque_atual" name="estoque_atual" 
                                       min="0" value="{{ produto.estoque_atual if produto else '0' }}" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="estoque_minimo" class="form-label">
                                    <i class="fas fa-exclamation-triangle me-1"></i>
                                    Estoque Mínimo *
                                </label>
                                <input type="number" class="form-control" id="estoque_minimo" name="estoque_minimo" 
                                       min="0" value="{{ produto.estoque_minimo if produto else '5' }}" required>
                                <small class="text-muted">Alerta quando estoque atingir este valor</small>
                            </div>
                        </div>
                    </div>

                    <div class="form-group mb-4">
                        <label for="foto" class="form-label">
                            <i class="fas fa-image me-1"></i>
                            Foto do Produto
                        </label>
                        <input type="file" class="form-control" id="foto" name="foto" accept="image/*">
                        <small class="text-muted">Formatos aceitos: JPG, PNG, GIF (máx. 16MB)</small>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('produtos') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>
                            Cancelar
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            {{ 'Atualizar' if produto else 'Cadastrar' }} Produto
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- Preview do Produto -->
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-eye me-2"></i>
                    Preview do Produto
                </h6>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    <div id="preview-foto" class="bg-light rounded d-flex align-items-center justify-content-center" 
                         style="width: 100%; height: 200px;">
                        {% if produto and produto.foto %}
                        <img src="{{ url_for('static', filename='img/produtos/' + produto.foto) }}" 
                             alt="{{ produto.nome }}" class="img-fluid rounded" style="max-height: 200px;">
                        {% else %}
                        <i class="fas fa-image fa-3x text-muted"></i>
                        {% endif %}
                    </div>
                </div>
                
                <h5 id="preview-nome" class="text-center">{{ produto.nome if produto else 'Nome do Produto' }}</h5>
                <p id="preview-descricao" class="text-muted text-center">
                    {{ produto.descricao if produto else 'Descrição do produto aparecerá aqui...' }}
                </p>
                
                <div class="row text-center">
                    <div class="col-6">
                        <small class="text-muted">Preço de Custo</small>
                        <div class="h6" id="preview-custo">R$ {{ "%.2f"|format(produto.preco_custo) if produto else '0,00' }}</div>
                    </div>
                    <div class="col-6">
                        <small class="text-muted">Preço de Venda</small>
                        <div class="h6 text-success" id="preview-venda">R$ {{ "%.2f"|format(produto.preco_venda) if produto else '0,00' }}</div>
                    </div>
                </div>
                
                <div class="row text-center mt-3">
                    <div class="col-6">
                        <small class="text-muted">Estoque Atual</small>
                        <div class="h6" id="preview-estoque">{{ produto.estoque_atual if produto else '0' }}</div>
                    </div>
                    <div class="col-6">
                        <small class="text-muted">Estoque Mínimo</small>
                        <div class="h6" id="preview-minimo">{{ produto.estoque_minimo if produto else '5' }}</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Dicas -->
        <div class="card shadow mt-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-info">
                    <i class="fas fa-lightbulb me-2"></i>
                    Dicas
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        <small>Use nomes descritivos para facilitar a busca</small>
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        <small>Defina um estoque mínimo adequado para evitar faltas</small>
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        <small>Adicione uma foto para melhor identificação</small>
                    </li>
                    <li>
                        <i class="fas fa-check text-success me-2"></i>
                        <small>O preço é calculado automaticamente com base no lucro</small>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Atualizar preview em tempo real
document.getElementById('nome').addEventListener('input', function() {
    document.getElementById('preview-nome').textContent = this.value || 'Nome do Produto';
});

document.getElementById('descricao').addEventListener('input', function() {
    document.getElementById('preview-descricao').textContent = this.value || 'Descrição do produto aparecerá aqui...';
});

document.getElementById('preco_custo').addEventListener('input', function() {
    document.getElementById('preview-custo').textContent = 'R$ ' + parseFloat(this.value || 0).toFixed(2).replace('.', ',');
});

document.getElementById('estoque_atual').addEventListener('input', function() {
    document.getElementById('preview-estoque').textContent = this.value || '0';
});

document.getElementById('estoque_minimo').addEventListener('input', function() {
    document.getElementById('preview-minimo').textContent = this.value || '5';
});

// Preview da foto
document.getElementById('foto').addEventListener('change', function() {
    const file = this.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById('preview-foto').innerHTML = 
                '<img src="' + e.target.result + '" alt="Preview" class="img-fluid rounded" style="max-height: 200px;">';
        };
        reader.readAsDataURL(file);
    }
});

// Calcular preço de venda
function calcularPrecoVenda() {
    const precoCusto = parseFloat(document.getElementById('preco_custo').value) || 0;
    const lucroDesejado = parseFloat(document.getElementById('lucro_desejado').value) || 0;
    const tipoLucro = document.getElementById('tipo_lucro').value;
    
    let precoVenda = 0;
    
    if (tipoLucro === 'percentual') {
        precoVenda = precoCusto * (1 + lucroDesejado / 100);
    } else {
        precoVenda = precoCusto + lucroDesejado;
    }
    
    document.getElementById('preco_venda').value = precoVenda.toFixed(2);
    document.getElementById('preview-venda').textContent = 'R$ ' + precoVenda.toFixed(2).replace('.', ',');
}

// Validação do formulário
document.getElementById('form-produto').addEventListener('submit', function(e) {
    const precoCusto = parseFloat(document.getElementById('preco_custo').value);
    const lucroDesejado = parseFloat(document.getElementById('lucro_desejado').value);
    
    if (precoCusto <= 0) {
        e.preventDefault();
        alert('O preço de custo deve ser maior que zero.');
        return;
    }
    
    if (lucroDesejado < 0) {
        e.preventDefault();
        alert('O lucro desejado não pode ser negativo.');
        return;
    }
    
    calcularPrecoVenda();
});
</script>
{% endblock %}
