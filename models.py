from database import db
import bcrypt
from datetime import datetime
import json

class Usuario:
    def __init__(self, id=None, nome=None, email=None, senha=None, tipo=None, ativo=True):
        self.id = id
        self.nome = nome
        self.email = email
        self.senha = senha
        self.tipo = tipo
        self.ativo = ativo
    
    @staticmethod
    def criar(nome, email, senha, tipo):
        """Cria um novo usuário"""
        conn = db.get_connection()
        cursor = conn.cursor()
        
        # Hash da senha
        senha_hash = bcrypt.hashpw(senha.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
        
        cursor.execute('''
            INSERT INTO usuarios (nome, email, senha, tipo)
            VALUES (?, ?, ?, ?)
        ''', (nome, email, senha_hash, tipo))
        
        user_id = cursor.lastrowid
        conn.commit()
        conn.close()
        
        return user_id
    
    @staticmethod
    def buscar_por_email(email):
        """Busca usuário por email"""
        conn = db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM usuarios WHERE email = ? AND ativo = 1', (email,))
        row = cursor.fetchone()
        conn.close()
        
        if row:
            return Usuario(
                id=row['id'],
                nome=row['nome'],
                email=row['email'],
                senha=row['senha'],
                tipo=row['tipo'],
                ativo=row['ativo']
            )
        return None
    
    @staticmethod
    def buscar_por_id(user_id):
        """Busca usuário por ID"""
        conn = db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM usuarios WHERE id = ? AND ativo = 1', (user_id,))
        row = cursor.fetchone()
        conn.close()
        
        if row:
            return Usuario(
                id=row['id'],
                nome=row['nome'],
                email=row['email'],
                senha=row['senha'],
                tipo=row['tipo'],
                ativo=row['ativo']
            )
        return None
    
    @staticmethod
    def listar_vendedores():
        """Lista todos os vendedores ativos"""
        conn = db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, nome FROM usuarios 
            WHERE tipo IN ('vendedor', 'gerente', 'admin') AND ativo = 1
            ORDER BY nome
        ''')
        
        vendedores = cursor.fetchall()
        conn.close()
        
        return [{'id': row['id'], 'nome': row['nome']} for row in vendedores]
    
    def verificar_senha(self, senha):
        """Verifica se a senha está correta"""
        return bcrypt.checkpw(senha.encode('utf-8'), self.senha.encode('utf-8'))

class Produto:
    @staticmethod
    def criar(nome, descricao, preco_custo, lucro_desejado, tipo_lucro, categoria_id, estoque_atual, estoque_minimo, foto=None):
        """Cria um novo produto"""
        conn = db.get_connection()
        cursor = conn.cursor()
        
        # Calcular preço de venda
        if tipo_lucro == 'percentual':
            preco_venda = preco_custo * (1 + lucro_desejado / 100)
        else:
            preco_venda = preco_custo + lucro_desejado
        
        cursor.execute('''
            INSERT INTO produtos (nome, descricao, preco_custo, lucro_desejado, tipo_lucro, 
                                preco_venda, categoria_id, estoque_atual, estoque_minimo, foto)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (nome, descricao, preco_custo, lucro_desejado, tipo_lucro, 
              preco_venda, categoria_id, estoque_atual, estoque_minimo, foto))
        
        produto_id = cursor.lastrowid
        conn.commit()
        conn.close()
        
        return produto_id
    
    @staticmethod
    def listar():
        """Lista todos os produtos ativos"""
        conn = db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT p.*, c.nome as categoria_nome
            FROM produtos p
            LEFT JOIN categorias c ON p.categoria_id = c.id
            WHERE p.ativo = 1
            ORDER BY p.nome
        ''')
        
        produtos = cursor.fetchall()
        conn.close()
        
        return [dict(row) for row in produtos]
    
    @staticmethod
    def buscar_por_id(produto_id):
        """Busca produto por ID"""
        conn = db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT p.*, c.nome as categoria_nome
            FROM produtos p
            LEFT JOIN categorias c ON p.categoria_id = c.id
            WHERE p.id = ? AND p.ativo = 1
        ''', (produto_id,))
        
        row = cursor.fetchone()
        conn.close()
        
        return dict(row) if row else None

class Cliente:
    @staticmethod
    def criar(nome, telefone, email=None, logradouro=None, numero=None, bairro=None, cidade=None, uf=None, cep=None):
        """Cria um novo cliente"""
        conn = db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO clientes (nome, telefone, email, logradouro, numero, bairro, cidade, uf, cep)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (nome, telefone, email, logradouro, numero, bairro, cidade, uf, cep))
        
        cliente_id = cursor.lastrowid
        conn.commit()
        conn.close()
        
        return cliente_id
    
    @staticmethod
    def listar():
        """Lista todos os clientes"""
        conn = db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM clientes ORDER BY nome')
        clientes = cursor.fetchall()
        conn.close()
        
        return [dict(row) for row in clientes]
    
    @staticmethod
    def buscar_por_id(cliente_id):
        """Busca cliente por ID"""
        conn = db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM clientes WHERE id = ?', (cliente_id,))
        row = cursor.fetchone()
        conn.close()
        
        return dict(row) if row else None

class Agendamento:
    @staticmethod
    def criar(cliente_id, produto_id, vendedor_id, data_agendamento, observacoes=None):
        """Cria um novo agendamento"""
        conn = db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO agendamentos (cliente_id, produto_id, vendedor_id, data_agendamento, observacoes)
            VALUES (?, ?, ?, ?, ?)
        ''', (cliente_id, produto_id, vendedor_id, data_agendamento, observacoes))
        
        agendamento_id = cursor.lastrowid
        conn.commit()
        conn.close()
        
        return agendamento_id
    
    @staticmethod
    def listar():
        """Lista todos os agendamentos"""
        conn = db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT a.*, c.nome as cliente_nome, c.telefone as cliente_telefone,
                   p.nome as produto_nome, u.nome as vendedor_nome
            FROM agendamentos a
            JOIN clientes c ON a.cliente_id = c.id
            JOIN produtos p ON a.produto_id = p.id
            JOIN usuarios u ON a.vendedor_id = u.id
            ORDER BY a.data_agendamento DESC
        ''')
        
        agendamentos = cursor.fetchall()
        conn.close()
        
        return [dict(row) for row in agendamentos]

class Venda:
    @staticmethod
    def criar(cliente_id, vendedor_id, itens, desconto_total=0, observacoes=None):
        """Cria uma nova venda com seus itens"""
        conn = db.get_connection()
        cursor = conn.cursor()

        try:
            # Calcular subtotal
            subtotal = 0
            for item in itens:
                produto = Produto.buscar_por_id(item['produto_id'])
                if not produto:
                    raise Exception(f"Produto {item['produto_id']} não encontrado")

                # Verificar estoque
                if produto['estoque_atual'] < item['quantidade']:
                    raise Exception(f"Estoque insuficiente para {produto['nome']}. Disponível: {produto['estoque_atual']}")

                subtotal += item['preco_unitario'] * item['quantidade']

            # Calcular total final
            desconto_itens = sum(item.get('desconto_item', 0) for item in itens)
            total_final = subtotal - desconto_itens - desconto_total

            # Inserir venda
            cursor.execute('''
                INSERT INTO vendas (cliente_id, vendedor_id, subtotal, desconto_total, total_final, observacoes)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (cliente_id, vendedor_id, subtotal, desconto_total, total_final, observacoes))

            venda_id = cursor.lastrowid

            # Inserir itens da venda e atualizar estoque
            for item in itens:
                subtotal_item = (item['preco_unitario'] * item['quantidade']) - item.get('desconto_item', 0)

                cursor.execute('''
                    INSERT INTO itens_venda (venda_id, produto_id, quantidade, preco_unitario, desconto_item, subtotal_item)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (venda_id, item['produto_id'], item['quantidade'],
                      item['preco_unitario'], item.get('desconto_item', 0), subtotal_item))

                # Atualizar estoque
                cursor.execute('''
                    UPDATE produtos SET estoque_atual = estoque_atual - ?
                    WHERE id = ?
                ''', (item['quantidade'], item['produto_id']))

            conn.commit()
            return venda_id

        except Exception as e:
            conn.rollback()
            raise e
        finally:
            conn.close()

    @staticmethod
    def listar():
        """Lista todas as vendas"""
        conn = db.get_connection()
        cursor = conn.cursor()

        cursor.execute('''
            SELECT v.*, c.nome as cliente_nome, c.telefone as cliente_telefone,
                   u.nome as vendedor_nome
            FROM vendas v
            JOIN clientes c ON v.cliente_id = c.id
            JOIN usuarios u ON v.vendedor_id = u.id
            ORDER BY v.data_venda DESC
        ''')

        vendas = cursor.fetchall()
        conn.close()

        return [dict(row) for row in vendas]

    @staticmethod
    def buscar_por_id(venda_id):
        """Busca venda por ID com seus itens"""
        conn = db.get_connection()
        cursor = conn.cursor()

        # Buscar venda
        cursor.execute('''
            SELECT v.*, c.nome as cliente_nome, c.telefone as cliente_telefone,
                   c.email as cliente_email, u.nome as vendedor_nome
            FROM vendas v
            JOIN clientes c ON v.cliente_id = c.id
            JOIN usuarios u ON v.vendedor_id = u.id
            WHERE v.id = ?
        ''', (venda_id,))

        venda = cursor.fetchone()
        if not venda:
            conn.close()
            return None

        venda = dict(venda)

        # Buscar itens da venda
        cursor.execute('''
            SELECT iv.*, p.nome as produto_nome
            FROM itens_venda iv
            JOIN produtos p ON iv.produto_id = p.id
            WHERE iv.venda_id = ?
        ''', (venda_id,))

        itens = [dict(row) for row in cursor.fetchall()]
        venda['itens'] = itens

        conn.close()
        return venda

class Categoria:
    @staticmethod
    def listar():
        """Lista todas as categorias ativas"""
        conn = db.get_connection()
        cursor = conn.cursor()

        cursor.execute('SELECT * FROM categorias WHERE ativo = 1 ORDER BY nome')
        categorias = cursor.fetchall()
        conn.close()

        return [dict(row) for row in categorias]
