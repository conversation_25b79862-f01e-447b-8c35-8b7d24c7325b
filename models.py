from database import db
import bcrypt
from datetime import datetime
import json

class Usuario:
    def __init__(self, id=None, nome=None, email=None, senha=None, tipo=None, ativo=True):
        self.id = id
        self.nome = nome
        self.email = email
        self.senha = senha
        self.tipo = tipo
        self.ativo = ativo
    
    @staticmethod
    def criar(nome, email, senha, tipo):
        """Cria um novo usuário"""
        conn = db.get_connection()
        cursor = conn.cursor()
        
        # Hash da senha
        senha_hash = bcrypt.hashpw(senha.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
        
        cursor.execute('''
            INSERT INTO usuarios (nome, email, senha, tipo)
            VALUES (?, ?, ?, ?)
        ''', (nome, email, senha_hash, tipo))
        
        user_id = cursor.lastrowid
        conn.commit()
        conn.close()
        
        return user_id
    
    @staticmethod
    def buscar_por_email(email):
        """Busca usuário por email"""
        conn = db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM usuarios WHERE email = ? AND ativo = 1', (email,))
        row = cursor.fetchone()
        conn.close()
        
        if row:
            return Usuario(
                id=row['id'],
                nome=row['nome'],
                email=row['email'],
                senha=row['senha'],
                tipo=row['tipo'],
                ativo=row['ativo']
            )
        return None
    
    @staticmethod
    def buscar_por_id(user_id):
        """Busca usuário por ID"""
        conn = db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM usuarios WHERE id = ? AND ativo = 1', (user_id,))
        row = cursor.fetchone()
        conn.close()
        
        if row:
            return Usuario(
                id=row['id'],
                nome=row['nome'],
                email=row['email'],
                senha=row['senha'],
                tipo=row['tipo'],
                ativo=row['ativo']
            )
        return None
    
    @staticmethod
    def listar_vendedores():
        """Lista todos os vendedores ativos"""
        conn = db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, nome FROM usuarios 
            WHERE tipo IN ('vendedor', 'gerente', 'admin') AND ativo = 1
            ORDER BY nome
        ''')
        
        vendedores = cursor.fetchall()
        conn.close()
        
        return [{'id': row['id'], 'nome': row['nome']} for row in vendedores]
    
    def verificar_senha(self, senha):
        """Verifica se a senha está correta"""
        return bcrypt.checkpw(senha.encode('utf-8'), self.senha.encode('utf-8'))

class Produto:
    @staticmethod
    def criar(nome, descricao, preco_custo, lucro_desejado, tipo_lucro, categoria_id, estoque_atual, estoque_minimo, foto=None):
        """Cria um novo produto"""
        conn = db.get_connection()
        cursor = conn.cursor()
        
        # Calcular preço de venda
        if tipo_lucro == 'percentual':
            preco_venda = preco_custo * (1 + lucro_desejado / 100)
        else:
            preco_venda = preco_custo + lucro_desejado
        
        cursor.execute('''
            INSERT INTO produtos (nome, descricao, preco_custo, lucro_desejado, tipo_lucro, 
                                preco_venda, categoria_id, estoque_atual, estoque_minimo, foto)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (nome, descricao, preco_custo, lucro_desejado, tipo_lucro, 
              preco_venda, categoria_id, estoque_atual, estoque_minimo, foto))
        
        produto_id = cursor.lastrowid
        conn.commit()
        conn.close()
        
        return produto_id
    
    @staticmethod
    def listar():
        """Lista todos os produtos ativos"""
        conn = db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT p.*, c.nome as categoria_nome
            FROM produtos p
            LEFT JOIN categorias c ON p.categoria_id = c.id
            WHERE p.ativo = 1
            ORDER BY p.nome
        ''')
        
        produtos = cursor.fetchall()
        conn.close()
        
        return [dict(row) for row in produtos]
    
    @staticmethod
    def buscar_por_id(produto_id):
        """Busca produto por ID"""
        conn = db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT p.*, c.nome as categoria_nome
            FROM produtos p
            LEFT JOIN categorias c ON p.categoria_id = c.id
            WHERE p.id = ? AND p.ativo = 1
        ''', (produto_id,))
        
        row = cursor.fetchone()
        conn.close()
        
        return dict(row) if row else None

class Cliente:
    @staticmethod
    def criar(nome, telefone, email=None, logradouro=None, numero=None, bairro=None, cidade=None, uf=None, cep=None):
        """Cria um novo cliente"""
        conn = db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO clientes (nome, telefone, email, logradouro, numero, bairro, cidade, uf, cep)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (nome, telefone, email, logradouro, numero, bairro, cidade, uf, cep))
        
        cliente_id = cursor.lastrowid
        conn.commit()
        conn.close()
        
        return cliente_id
    
    @staticmethod
    def listar():
        """Lista todos os clientes"""
        conn = db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM clientes ORDER BY nome')
        clientes = cursor.fetchall()
        conn.close()
        
        return [dict(row) for row in clientes]
    
    @staticmethod
    def buscar_por_id(cliente_id):
        """Busca cliente por ID"""
        conn = db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM clientes WHERE id = ?', (cliente_id,))
        row = cursor.fetchone()
        conn.close()
        
        return dict(row) if row else None

class Agendamento:
    @staticmethod
    def criar(cliente_id, produto_id, vendedor_id, data_agendamento, observacoes=None):
        """Cria um novo agendamento"""
        conn = db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO agendamentos (cliente_id, produto_id, vendedor_id, data_agendamento, observacoes)
            VALUES (?, ?, ?, ?, ?)
        ''', (cliente_id, produto_id, vendedor_id, data_agendamento, observacoes))
        
        agendamento_id = cursor.lastrowid
        conn.commit()
        conn.close()
        
        return agendamento_id
    
    @staticmethod
    def listar():
        """Lista todos os agendamentos"""
        conn = db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT a.*, c.nome as cliente_nome, c.telefone as cliente_telefone,
                   p.nome as produto_nome, u.nome as vendedor_nome
            FROM agendamentos a
            JOIN clientes c ON a.cliente_id = c.id
            JOIN produtos p ON a.produto_id = p.id
            JOIN usuarios u ON a.vendedor_id = u.id
            ORDER BY a.data_agendamento DESC
        ''')
        
        agendamentos = cursor.fetchall()
        conn.close()
        
        return [dict(row) for row in agendamentos]

class Categoria:
    @staticmethod
    def listar():
        """Lista todas as categorias ativas"""
        conn = db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM categorias WHERE ativo = 1 ORDER BY nome')
        categorias = cursor.fetchall()
        conn.close()
        
        return [dict(row) for row in categorias]
