{% extends "base.html" %}

{% block title %}Relatório de Vendas por Período - Saúde Flex{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-chart-line me-2 text-success"></i>
                Relatório de Vendas por Período
            </h1>
            <div class="btn-group" role="group">
                <a href="{{ url_for('relatorios') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>
                    Voltar
                </a>
                <button type="button" class="btn btn-outline-primary" onclick="exportarRelatorio()">
                    <i class="fas fa-download me-2"></i>
                    Exportar PDF
                </button>
                <button type="button" class="btn btn-outline-success" onclick="exportarExcel()">
                    <i class="fas fa-file-excel me-2"></i>
                    Exportar Excel
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Filtros de Período -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-filter me-2"></i>
                    Filtros de Período
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="data-inicio">Data Início</label>
                            <input type="date" class="form-control" id="data-inicio" value="{{ filtros.data_inicio if filtros.data_inicio else '' }}">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="data-fim">Data Fim</label>
                            <input type="date" class="form-control" id="data-fim" value="{{ filtros.data_fim if filtros.data_fim else '' }}">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="vendedor-filtro">Vendedor</label>
                            <select class="form-control" id="vendedor-filtro">
                                <option value="">Todos os vendedores</option>
                                {% for vendedor in vendedores %}
                                <option value="{{ vendedor.id }}" {% if filtros.vendedor_id == vendedor.id|string %}selected{% endif %}>
                                    {{ vendedor.nome }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label>&nbsp;</label>
                            <button type="button" class="btn btn-primary w-100" onclick="aplicarFiltros()">
                                <i class="fas fa-search me-2"></i>
                                Aplicar Filtros
                            </button>
                        </div>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-12">
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-info" onclick="setPeriodo('hoje')">Hoje</button>
                            <button type="button" class="btn btn-outline-info" onclick="setPeriodo('semana')">Esta Semana</button>
                            <button type="button" class="btn btn-outline-info" onclick="setPeriodo('mes')">Este Mês</button>
                            <button type="button" class="btn btn-outline-info" onclick="setPeriodo('trimestre')">Trimestre</button>
                            <button type="button" class="btn btn-outline-info" onclick="setPeriodo('ano')">Este Ano</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Resumo Executivo -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Faturamento Total
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="faturamento-total">
                            R$ {{ "%.2f"|format(estatisticas.total_faturamento) if estatisticas else "0,00" }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Total de Vendas
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="total-vendas">
                            {{ estatisticas.total_vendas if estatisticas else 0 }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Ticket Médio
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="ticket-medio">
                            R$ {{ "%.2f"|format(estatisticas.ticket_medio) if estatisticas else "0,00" }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Lucro Total
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="lucro-total">
                            R$ 13.575,00
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-chart-pie fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Gráficos -->
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-chart-area me-2"></i>
                    Evolução das Vendas
                </h6>
            </div>
            <div class="card-body">
                <canvas id="graficoVendasPeriodo" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
    <div class="col-lg-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-chart-pie me-2"></i>
                    Vendas por Vendedor
                </h6>
            </div>
            <div class="card-body">
                <canvas id="graficoVendedores" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Tabela Detalhada -->
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-table me-2"></i>
                    Detalhamento das Vendas
                </h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="tabela-vendas-detalhada">
                        <thead>
                            <tr>
                                <th>Data</th>
                                <th>Cliente</th>
                                <th>Vendedor</th>
                                <th>Produtos</th>
                                <th>Subtotal</th>
                                <th>Desconto</th>
                                <th>Total</th>
                                <th>Lucro</th>
                                <th>Ações</th>
                            </tr>
                        </thead>
                        <tbody id="tbody-vendas">
                            {% if vendas %}
                                {% for venda in vendas %}
                                <tr>
                                    <td>{{ venda.data_venda.strftime('%d/%m/%Y') if venda.data_venda else 'N/A' }}</td>
                                    <td>{{ venda.cliente_nome }}</td>
                                    <td>{{ venda.vendedor_nome }}</td>
                                    <td><span class="badge bg-info">Ver itens</span></td>
                                    <td>R$ {{ "%.2f"|format(venda.subtotal) }}</td>
                                    <td>R$ {{ "%.2f"|format(venda.desconto_total) }}</td>
                                    <td><strong class="text-success">R$ {{ "%.2f"|format(venda.total_final) }}</strong></td>
                                    <td><strong class="text-warning">R$ 0,00</strong></td>
                                    <td>
                                        <a href="{{ url_for('venda_detalhes', venda_id=venda.id) }}" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            {% else %}
                                <tr>
                                    <td colspan="9" class="text-center text-muted py-4">
                                        <i class="fas fa-chart-line fa-2x mb-2"></i><br>
                                        Nenhuma venda encontrada no período selecionado
                                    </td>
                                </tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal de Detalhes da Venda -->
<div class="modal fade" id="modalDetalhesVenda" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-receipt me-2"></i>
                    Detalhes da Venda
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="conteudo-detalhes-venda">
                <!-- Conteúdo será carregado via JavaScript -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
                <button type="button" class="btn btn-info" onclick="imprimirComprovante()">
                    <i class="fas fa-print me-2"></i>
                    Imprimir Comprovante
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}
.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}
.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}
.text-xs {
    font-size: 0.7rem;
}
.text-gray-800 {
    color: #5a5c69 !important;
}
.text-gray-300 {
    color: #dddfeb !important;
}
</style>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Dados simulados para demonstração
const dadosVendas = [
    {id: 1, data: '2024-06-15', cliente: 'Maria Silva', vendedor: 'João Santos', produtos: 3, subtotal: 450.00, desconto: 45.00, total: 405.00, lucro: 121.50},
    {id: 2, data: '2024-06-14', cliente: 'Pedro Costa', vendedor: 'Ana Lima', produtos: 2, subtotal: 320.00, desconto: 0.00, total: 320.00, lucro: 96.00},
    {id: 3, data: '2024-06-13', cliente: 'Carlos Oliveira', vendedor: 'João Santos', produtos: 1, subtotal: 180.00, desconto: 18.00, total: 162.00, lucro: 48.60},
    {id: 4, data: '2024-06-12', cliente: 'Fernanda Souza', vendedor: 'Maria Pereira', produtos: 4, subtotal: 680.00, desconto: 68.00, total: 612.00, lucro: 183.60},
    {id: 5, data: '2024-06-11', cliente: 'Roberto Lima', vendedor: 'Ana Lima', produtos: 2, subtotal: 290.00, desconto: 0.00, total: 290.00, lucro: 87.00}
];

// Gráfico de Evolução das Vendas
const ctxVendas = document.getElementById('graficoVendasPeriodo').getContext('2d');
const graficoVendas = new Chart(ctxVendas, {
    type: 'line',
    data: {
        labels: ['11/06', '12/06', '13/06', '14/06', '15/06'],
        datasets: [{
            label: 'Faturamento (R$)',
            data: [290, 612, 162, 320, 405],
            borderColor: '#1cc88a',
            backgroundColor: 'rgba(28, 200, 138, 0.1)',
            tension: 0.3,
            fill: true
        }, {
            label: 'Lucro (R$)',
            data: [87, 183.60, 48.60, 96, 121.50],
            borderColor: '#f6c23e',
            backgroundColor: 'rgba(246, 194, 62, 0.1)',
            tension: 0.3,
            fill: true
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'top'
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return 'R$ ' + value.toLocaleString('pt-BR');
                    }
                }
            }
        }
    }
});

// Gráfico de Vendas por Vendedor
const ctxVendedores = document.getElementById('graficoVendedores').getContext('2d');
const graficoVendedores = new Chart(ctxVendedores, {
    type: 'doughnut',
    data: {
        labels: ['João Santos', 'Ana Lima', 'Maria Pereira'],
        datasets: [{
            data: [567, 610, 612],
            backgroundColor: ['#4e73df', '#1cc88a', '#36b9cc']
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

// Carregar dados na tabela
function carregarTabelaVendas() {
    const tbody = document.getElementById('tbody-vendas');
    tbody.innerHTML = '';
    
    dadosVendas.forEach(venda => {
        const tr = document.createElement('tr');
        tr.innerHTML = `
            <td>${new Date(venda.data).toLocaleDateString('pt-BR')}</td>
            <td>${venda.cliente}</td>
            <td>${venda.vendedor}</td>
            <td><span class="badge bg-info">${venda.produtos} itens</span></td>
            <td>R$ ${venda.subtotal.toFixed(2)}</td>
            <td>R$ ${venda.desconto.toFixed(2)}</td>
            <td><strong class="text-success">R$ ${venda.total.toFixed(2)}</strong></td>
            <td><strong class="text-warning">R$ ${venda.lucro.toFixed(2)}</strong></td>
            <td>
                <button class="btn btn-sm btn-outline-primary" onclick="verDetalhesVenda(${venda.id})">
                    <i class="fas fa-eye"></i>
                </button>
            </td>
        `;
        tbody.appendChild(tr);
    });
}

// Funções de filtro
function setPeriodo(periodo) {
    const hoje = new Date();
    const dataInicio = document.getElementById('data-inicio');
    const dataFim = document.getElementById('data-fim');
    
    dataFim.value = hoje.toISOString().split('T')[0];
    
    switch(periodo) {
        case 'hoje':
            dataInicio.value = hoje.toISOString().split('T')[0];
            break;
        case 'semana':
            const inicioSemana = new Date(hoje.setDate(hoje.getDate() - 7));
            dataInicio.value = inicioSemana.toISOString().split('T')[0];
            break;
        case 'mes':
            const inicioMes = new Date(hoje.getFullYear(), hoje.getMonth(), 1);
            dataInicio.value = inicioMes.toISOString().split('T')[0];
            break;
        case 'trimestre':
            const inicioTrimestre = new Date(hoje.getFullYear(), Math.floor(hoje.getMonth() / 3) * 3, 1);
            dataInicio.value = inicioTrimestre.toISOString().split('T')[0];
            break;
        case 'ano':
            const inicioAno = new Date(hoje.getFullYear(), 0, 1);
            dataInicio.value = inicioAno.toISOString().split('T')[0];
            break;
    }
    
    aplicarFiltros();
}

function aplicarFiltros() {
    const dataInicio = document.getElementById('data-inicio').value;
    const dataFim = document.getElementById('data-fim').value;
    const vendedorId = document.getElementById('vendedor-filtro').value;

    // Construir URL com parâmetros
    const params = new URLSearchParams();
    if (dataInicio) params.append('data_inicio', dataInicio);
    if (dataFim) params.append('data_fim', dataFim);
    if (vendedorId) params.append('vendedor_id', vendedorId);

    // Redirecionar com filtros
    window.location.href = '{{ url_for("relatorio_vendas_periodo") }}?' + params.toString();
}

function verDetalhesVenda(id) {
    const venda = dadosVendas.find(v => v.id === id);
    if (!venda) return;
    
    const conteudo = `
        <div class="row">
            <div class="col-md-6">
                <h6><i class="fas fa-info-circle me-2"></i>Informações da Venda</h6>
                <p><strong>Data:</strong> ${new Date(venda.data).toLocaleDateString('pt-BR')}</p>
                <p><strong>Cliente:</strong> ${venda.cliente}</p>
                <p><strong>Vendedor:</strong> ${venda.vendedor}</p>
            </div>
            <div class="col-md-6">
                <h6><i class="fas fa-calculator me-2"></i>Valores</h6>
                <p><strong>Subtotal:</strong> R$ ${venda.subtotal.toFixed(2)}</p>
                <p><strong>Desconto:</strong> R$ ${venda.desconto.toFixed(2)}</p>
                <p><strong>Total:</strong> <span class="text-success">R$ ${venda.total.toFixed(2)}</span></p>
                <p><strong>Lucro:</strong> <span class="text-warning">R$ ${venda.lucro.toFixed(2)}</span></p>
            </div>
        </div>
    `;
    
    document.getElementById('conteudo-detalhes-venda').innerHTML = conteudo;
    
    const modal = new bootstrap.Modal(document.getElementById('modalDetalhesVenda'));
    modal.show();
}

function exportarRelatorio() {
    SaudeFlex.notify.info('Gerando relatório PDF...');
    setTimeout(() => {
        SaudeFlex.notify.success('Relatório PDF gerado com sucesso!');
    }, 2000);
}

function exportarExcel() {
    SaudeFlex.notify.info('Gerando planilha Excel...');
    setTimeout(() => {
        SaudeFlex.notify.success('Planilha Excel gerada com sucesso!');
    }, 2000);
}

function imprimirComprovante() {
    SaudeFlex.notify.info('Imprimindo comprovante...');
}

// Inicialização
document.addEventListener('DOMContentLoaded', function() {
    // Definir período padrão (últimos 30 dias)
    setPeriodo('mes');
    carregarTabelaVendas();
});
</script>
{% endblock %}
