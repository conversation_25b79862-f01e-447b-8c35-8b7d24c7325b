from flask import Flask, render_template, request, redirect, url_for, session, flash, jsonify, send_from_directory
from datetime import datetime
import os
import bcrypt
from config import AppConfig
from database import db
from models import Usuario, Produto, Cliente, Agendamento, Categoria, Venda
from backup_system import BackupSystem
from pdf_generator import PDFGenerator
import json

# Criar app Flask com configurações adaptadas para EXE
app = Flask(__name__,
           static_folder=AppConfig.STATIC_FOLDER,
           template_folder=AppConfig.TEMPLATE_FOLDER)

# Aplicar configurações
app.secret_key = AppConfig.SECRET_KEY
app.config['UPLOAD_FOLDER'] = AppConfig.UPLOAD_FOLDER
app.config['MAX_CONTENT_LENGTH'] = AppConfig.MAX_CONTENT_LENGTH

# Criar pastas necessárias se não existirem
os.makedirs(AppConfig.UPLOAD_FOLDER, exist_ok=True)
os.makedirs(AppConfig.BACKUP_PATH, exist_ok=True)

# Middleware para verificar autenticação
@app.before_request
def verificar_autenticacao():
    """Verifica se o usuário está autenticado"""
    rotas_publicas = ['login', 'static']
    
    if request.endpoint and any(rota in request.endpoint for rota in rotas_publicas):
        return
    
    if 'user_id' not in session:
        return redirect(url_for('login'))

# Context processor para disponibilizar dados globais nos templates
@app.context_processor
def inject_user():
    """Injeta dados do usuário logado em todos os templates"""
    if 'user_id' in session:
        usuario = Usuario.buscar_por_id(session['user_id'])
        return {'usuario_logado': usuario}
    return {'usuario_logado': None}

# Rotas de Autenticação
@app.route('/')
def index():
    """Página inicial - Dashboard"""
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    # Estatísticas para o dashboard
    conn = db.get_connection()
    cursor = conn.cursor()
    
    # Total de produtos
    cursor.execute('SELECT COUNT(*) as total FROM produtos WHERE ativo = 1')
    total_produtos = cursor.fetchone()['total']
    
    # Total de clientes
    cursor.execute('SELECT COUNT(*) as total FROM clientes')
    total_clientes = cursor.fetchone()['total']
    
    # Agendamentos hoje
    hoje = datetime.now().strftime('%Y-%m-%d')
    cursor.execute('''
        SELECT COUNT(*) as total FROM agendamentos 
        WHERE DATE(data_agendamento) = ? AND status = 'agendado'
    ''', (hoje,))
    agendamentos_hoje = cursor.fetchone()['total']
    
    # Produtos com estoque baixo
    cursor.execute('''
        SELECT COUNT(*) as total FROM produtos 
        WHERE estoque_atual <= estoque_minimo AND ativo = 1
    ''', )
    estoque_baixo = cursor.fetchone()['total']
    
    # Últimos agendamentos
    cursor.execute('''
        SELECT a.*, c.nome as cliente_nome, p.nome as produto_nome, u.nome as vendedor_nome
        FROM agendamentos a
        JOIN clientes c ON a.cliente_id = c.id
        JOIN produtos p ON a.produto_id = p.id
        JOIN usuarios u ON a.vendedor_id = u.id
        ORDER BY a.data_agendamento DESC
        LIMIT 5
    ''')
    ultimos_agendamentos = [dict(row) for row in cursor.fetchall()]
    
    conn.close()
    
    estatisticas = {
        'total_produtos': total_produtos,
        'total_clientes': total_clientes,
        'agendamentos_hoje': agendamentos_hoje,
        'estoque_baixo': estoque_baixo
    }
    
    return render_template('dashboard.html', 
                         estatisticas=estatisticas,
                         ultimos_agendamentos=ultimos_agendamentos)

@app.route('/login', methods=['GET', 'POST'])
def login():
    """Página de login"""
    if request.method == 'POST':
        email = request.form['email']
        senha = request.form['senha']
        
        usuario = Usuario.buscar_por_email(email)
        
        if usuario and usuario.verificar_senha(senha):
            session['user_id'] = usuario.id
            session['user_nome'] = usuario.nome
            session['user_tipo'] = usuario.tipo
            
            flash('Login realizado com sucesso!', 'success')
            return redirect(url_for('index'))
        else:
            flash('Email ou senha incorretos!', 'error')
    
    return render_template('login.html')

@app.route('/logout')
def logout():
    """Logout do usuário"""
    session.clear()
    flash('Logout realizado com sucesso!', 'success')
    return redirect(url_for('login'))

# Rotas de Produtos
@app.route('/produtos')
def produtos():
    """Lista de produtos"""
    produtos_lista = Produto.listar()
    categorias = Categoria.listar()
    return render_template('produtos/lista.html', produtos=produtos_lista, categorias=categorias)

@app.route('/produtos/novo', methods=['GET', 'POST'])
def produto_novo():
    """Cadastro de novo produto"""
    if request.method == 'POST':
        nome = request.form['nome']
        descricao = request.form['descricao']
        preco_custo = float(request.form['preco_custo'])
        lucro_desejado = float(request.form['lucro_desejado'])
        tipo_lucro = request.form['tipo_lucro']
        categoria_id = int(request.form['categoria_id']) if request.form['categoria_id'] else None
        estoque_atual = int(request.form['estoque_atual'])
        estoque_minimo = int(request.form['estoque_minimo'])
        
        produto_id = Produto.criar(nome, descricao, preco_custo, lucro_desejado, 
                                 tipo_lucro, categoria_id, estoque_atual, estoque_minimo)
        
        flash('Produto cadastrado com sucesso!', 'success')
        return redirect(url_for('produtos'))
    
    categorias = Categoria.listar()
    return render_template('produtos/form.html', categorias=categorias)

# Rotas de Clientes
@app.route('/clientes')
def clientes():
    """Lista de clientes"""
    clientes_lista = Cliente.listar()
    return render_template('clientes/lista.html', clientes=clientes_lista)

@app.route('/clientes/novo', methods=['GET', 'POST'])
def cliente_novo():
    """Cadastro de novo cliente"""
    if request.method == 'POST':
        nome = request.form['nome']
        telefone = request.form['telefone']
        email = request.form.get('email')
        logradouro = request.form.get('logradouro')
        numero = request.form.get('numero')
        bairro = request.form.get('bairro')
        cidade = request.form.get('cidade')
        uf = request.form.get('uf')
        cep = request.form.get('cep')
        
        cliente_id = Cliente.criar(nome, telefone, email, logradouro, 
                                 numero, bairro, cidade, uf, cep)
        
        flash('Cliente cadastrado com sucesso!', 'success')
        return redirect(url_for('clientes'))
    
    return render_template('clientes/form.html')

# Rotas de Agendamentos
@app.route('/agendamentos')
def agendamentos():
    """Lista de agendamentos"""
    agendamentos_lista = Agendamento.listar()
    return render_template('agendamentos/lista.html', agendamentos=agendamentos_lista)

@app.route('/agendamentos/novo', methods=['GET', 'POST'])
def agendamento_novo():
    """Cadastro de novo agendamento"""
    if request.method == 'POST':
        cliente_id = int(request.form['cliente_id'])
        produto_id = int(request.form['produto_id'])
        vendedor_id = int(request.form['vendedor_id'])
        data_agendamento = request.form['data_agendamento']
        observacoes = request.form.get('observacoes')
        
        agendamento_id = Agendamento.criar(cliente_id, produto_id, vendedor_id, 
                                         data_agendamento, observacoes)
        
        flash('Agendamento criado com sucesso!', 'success')
        return redirect(url_for('agendamentos'))
    
    clientes_lista = Cliente.listar()
    produtos_lista = Produto.listar()
    vendedores = Usuario.listar_vendedores()
    
    return render_template('agendamentos/form.html', 
                         clientes=clientes_lista,
                         produtos=produtos_lista,
                         vendedores=vendedores)

# Rotas de Vendas
@app.route('/vendas')
def vendas():
    """Lista de vendas"""
    vendas_lista = Venda.listar()
    return render_template('vendas/lista.html', vendas=vendas_lista)

@app.route('/vendas/nova', methods=['GET', 'POST'])
def venda_nova():
    """Nova venda"""
    if request.method == 'POST':
        try:
            data = request.get_json()

            cliente_id = data['cliente_id']
            vendedor_id = session['user_id']
            itens = data['itens']
            desconto_total = data.get('desconto_total', 0)
            observacoes = data.get('observacoes')

            # Validações
            if not cliente_id or not itens:
                return jsonify({'success': False, 'message': 'Cliente e itens são obrigatórios'}), 400

            # Criar venda
            venda_id = Venda.criar(cliente_id, vendedor_id, itens, desconto_total, observacoes)

            return jsonify({
                'success': True,
                'message': 'Venda realizada com sucesso!',
                'venda_id': venda_id
            })

        except Exception as e:
            return jsonify({'success': False, 'message': str(e)}), 500

    clientes_lista = Cliente.listar()
    produtos_lista = Produto.listar()

    return render_template('vendas/form.html',
                         clientes=clientes_lista,
                         produtos=produtos_lista)

@app.route('/vendas/<int:venda_id>')
def venda_detalhes(venda_id):
    """Detalhes da venda"""
    venda = Venda.buscar_por_id(venda_id)
    if not venda:
        flash('Venda não encontrada!', 'error')
        return redirect(url_for('vendas'))

    return render_template('vendas/detalhes.html', venda=venda)

@app.route('/vendas/<int:venda_id>/comprovante')
def gerar_comprovante(venda_id):
    """Gerar comprovante PDF da venda"""
    venda = Venda.buscar_por_id(venda_id)
    if not venda:
        flash('Venda não encontrada!', 'error')
        return redirect(url_for('vendas'))

    # Verificar se deve gerar PDF ou mostrar HTML
    formato = request.args.get('formato', 'html')

    if formato == 'pdf':
        try:
            pdf_generator = PDFGenerator()
            pdf_path = pdf_generator.gerar_comprovante_venda(venda)

            # Retornar arquivo PDF
            return send_from_directory(
                os.path.dirname(pdf_path),
                os.path.basename(pdf_path),
                as_attachment=True,
                download_name=f'comprovante_venda_{venda_id}.pdf'
            )
        except Exception as e:
            flash(f'Erro ao gerar PDF: {str(e)}', 'error')
            return redirect(url_for('venda_detalhes', venda_id=venda_id))

    # Mostrar comprovante em HTML
    return render_template('vendas/comprovante.html', venda=venda)

# Rotas de Relatórios
@app.route('/relatorios')
def relatorios():
    """Dashboard de relatórios"""
    return render_template('relatorios/dashboard.html')

@app.route('/relatorios/estoque-baixo')
def relatorio_estoque_baixo():
    """Relatório de produtos com estoque baixo"""
    conn = db.get_connection()
    cursor = conn.cursor()

    cursor.execute('''
        SELECT p.*, c.nome as categoria_nome
        FROM produtos p
        LEFT JOIN categorias c ON p.categoria_id = c.id
        WHERE p.estoque_atual <= p.estoque_minimo AND p.ativo = 1
        ORDER BY p.estoque_atual ASC
    ''')

    produtos_baixo_estoque = [dict(row) for row in cursor.fetchall()]
    conn.close()

    return render_template('relatorios/estoque_baixo.html', produtos=produtos_baixo_estoque)

@app.route('/relatorios/vendas-periodo')
def relatorio_vendas_periodo():
    """Relatório de vendas por período"""
    data_inicio = request.args.get('data_inicio')
    data_fim = request.args.get('data_fim')
    vendedor_id = request.args.get('vendedor_id')

    conn = db.get_connection()
    cursor = conn.cursor()

    # Query base
    query = '''
        SELECT v.*, c.nome as cliente_nome, c.telefone as cliente_telefone,
               u.nome as vendedor_nome, DATE(v.data_venda) as data_venda_formatada
        FROM vendas v
        JOIN clientes c ON v.cliente_id = c.id
        JOIN usuarios u ON v.vendedor_id = u.id
        WHERE 1=1
    '''
    params = []

    # Filtros
    if data_inicio:
        query += ' AND DATE(v.data_venda) >= ?'
        params.append(data_inicio)

    if data_fim:
        query += ' AND DATE(v.data_venda) <= ?'
        params.append(data_fim)

    if vendedor_id:
        query += ' AND v.vendedor_id = ?'
        params.append(vendedor_id)

    query += ' ORDER BY v.data_venda DESC'

    cursor.execute(query, params)
    vendas = [dict(row) for row in cursor.fetchall()]

    # Estatísticas
    total_vendas = len(vendas)
    total_faturamento = sum(venda['total_final'] for venda in vendas)
    ticket_medio = total_faturamento / total_vendas if total_vendas > 0 else 0

    # Vendedores para filtro
    cursor.execute('SELECT id, nome FROM usuarios WHERE tipo IN ("vendedor", "gerente", "admin") ORDER BY nome')
    vendedores = [dict(row) for row in cursor.fetchall()]

    conn.close()

    estatisticas = {
        'total_vendas': total_vendas,
        'total_faturamento': total_faturamento,
        'ticket_medio': ticket_medio
    }

    return render_template('relatorios/vendas_periodo.html',
                         vendas=vendas,
                         estatisticas=estatisticas,
                         vendedores=vendedores,
                         filtros={
                             'data_inicio': data_inicio,
                             'data_fim': data_fim,
                             'vendedor_id': vendedor_id
                         })

@app.route('/relatorios/produtos-mais-vendidos')
def relatorio_produtos_mais_vendidos():
    """Relatório de produtos mais vendidos"""
    data_inicio = request.args.get('data_inicio')
    data_fim = request.args.get('data_fim')
    categoria_id = request.args.get('categoria_id')

    conn = db.get_connection()
    cursor = conn.cursor()

    # Query para produtos mais vendidos
    query = '''
        SELECT p.id, p.nome as produto_nome, c.nome as categoria_nome,
               SUM(iv.quantidade) as quantidade_vendida,
               SUM(iv.subtotal_item) as receita_bruta,
               COUNT(DISTINCT v.id) as numero_vendas,
               AVG(iv.preco_unitario) as preco_medio
        FROM itens_venda iv
        JOIN produtos p ON iv.produto_id = p.id
        LEFT JOIN categorias c ON p.categoria_id = c.id
        JOIN vendas v ON iv.venda_id = v.id
        WHERE 1=1
    '''
    params = []

    # Filtros
    if data_inicio:
        query += ' AND DATE(v.data_venda) >= ?'
        params.append(data_inicio)

    if data_fim:
        query += ' AND DATE(v.data_venda) <= ?'
        params.append(data_fim)

    if categoria_id:
        query += ' AND p.categoria_id = ?'
        params.append(categoria_id)

    query += '''
        GROUP BY p.id, p.nome, c.nome
        ORDER BY quantidade_vendida DESC
        LIMIT 20
    '''

    cursor.execute(query, params)
    produtos = [dict(row) for row in cursor.fetchall()]

    # Categorias para filtro
    cursor.execute('SELECT * FROM categorias WHERE ativo = 1 ORDER BY nome')
    categorias = [dict(row) for row in cursor.fetchall()]

    conn.close()

    return render_template('relatorios/produtos_mais_vendidos.html',
                         produtos=produtos,
                         categorias=categorias,
                         filtros={
                             'data_inicio': data_inicio,
                             'data_fim': data_fim,
                             'categoria_id': categoria_id
                         })

@app.route('/relatorios/vendas-por-vendedor')
def relatorio_vendas_por_vendedor():
    """Relatório de vendas por vendedor"""
    data_inicio = request.args.get('data_inicio')
    data_fim = request.args.get('data_fim')

    conn = db.get_connection()
    cursor = conn.cursor()

    # Query para vendas por vendedor
    query = '''
        SELECT u.id, u.nome as vendedor_nome,
               COUNT(v.id) as quantidade_vendas,
               SUM(v.total_final) as valor_total,
               AVG(v.total_final) as ticket_medio,
               MIN(v.data_venda) as primeira_venda,
               MAX(v.data_venda) as ultima_venda
        FROM usuarios u
        LEFT JOIN vendas v ON u.id = v.vendedor_id
    '''
    params = []

    # Filtros
    where_conditions = ['u.tipo IN ("vendedor", "gerente", "admin")']

    if data_inicio:
        where_conditions.append('(v.data_venda IS NULL OR DATE(v.data_venda) >= ?)')
        params.append(data_inicio)

    if data_fim:
        where_conditions.append('(v.data_venda IS NULL OR DATE(v.data_venda) <= ?)')
        params.append(data_fim)

    if where_conditions:
        query += ' WHERE ' + ' AND '.join(where_conditions)

    query += '''
        GROUP BY u.id, u.nome
        ORDER BY valor_total DESC NULLS LAST
    '''

    cursor.execute(query, params)
    vendedores = [dict(row) for row in cursor.fetchall()]

    # Calcular totais gerais
    total_vendas = sum(v['quantidade_vendas'] or 0 for v in vendedores)
    total_faturamento = sum(v['valor_total'] or 0 for v in vendedores)

    conn.close()

    estatisticas = {
        'total_vendas': total_vendas,
        'total_faturamento': total_faturamento,
        'total_vendedores': len([v for v in vendedores if v['quantidade_vendas'] and v['quantidade_vendas'] > 0])
    }

    return render_template('relatorios/vendas_por_vendedor.html',
                         vendedores=vendedores,
                         estatisticas=estatisticas,
                         filtros={
                             'data_inicio': data_inicio,
                             'data_fim': data_fim
                         })

@app.route('/relatorios/lucro')
def relatorio_lucro():
    """Relatório de lucro"""
    data_inicio = request.args.get('data_inicio')
    data_fim = request.args.get('data_fim')
    produto_id = request.args.get('produto_id')

    conn = db.get_connection()
    cursor = conn.cursor()

    # Query para cálculo de lucro
    query = '''
        SELECT v.id as venda_id, v.data_venda, c.nome as cliente_nome,
               u.nome as vendedor_nome, p.nome as produto_nome,
               iv.quantidade, iv.preco_unitario, iv.desconto_item,
               p.preco_custo, iv.subtotal_item,
               (iv.preco_unitario - p.preco_custo) * iv.quantidade - iv.desconto_item as lucro_bruto,
               ((iv.preco_unitario - p.preco_custo) * iv.quantidade - iv.desconto_item) / iv.subtotal_item * 100 as margem_lucro
        FROM vendas v
        JOIN itens_venda iv ON v.id = iv.venda_id
        JOIN produtos p ON iv.produto_id = p.id
        JOIN clientes c ON v.cliente_id = c.id
        JOIN usuarios u ON v.vendedor_id = u.id
        WHERE 1=1
    '''
    params = []

    # Filtros
    if data_inicio:
        query += ' AND DATE(v.data_venda) >= ?'
        params.append(data_inicio)

    if data_fim:
        query += ' AND DATE(v.data_venda) <= ?'
        params.append(data_fim)

    if produto_id:
        query += ' AND p.id = ?'
        params.append(produto_id)

    query += ' ORDER BY v.data_venda DESC'

    cursor.execute(query, params)
    vendas_detalhadas = [dict(row) for row in cursor.fetchall()]

    # Resumo de lucro
    total_receita = sum(venda['subtotal_item'] for venda in vendas_detalhadas)
    total_lucro = sum(venda['lucro_bruto'] for venda in vendas_detalhadas)
    margem_media = (total_lucro / total_receita * 100) if total_receita > 0 else 0

    # Produtos para filtro
    cursor.execute('SELECT id, nome FROM produtos WHERE ativo = 1 ORDER BY nome')
    produtos = [dict(row) for row in cursor.fetchall()]

    conn.close()

    estatisticas = {
        'total_receita': total_receita,
        'total_lucro': total_lucro,
        'margem_media': margem_media,
        'total_vendas': len(set(venda['venda_id'] for venda in vendas_detalhadas))
    }

    return render_template('relatorios/lucro.html',
                         vendas=vendas_detalhadas,
                         estatisticas=estatisticas,
                         produtos=produtos,
                         filtros={
                             'data_inicio': data_inicio,
                             'data_fim': data_fim,
                             'produto_id': produto_id
                         })

# Rotas de Usuários (apenas para admin)
@app.route('/usuarios')
def usuarios():
    """Lista de usuários (apenas admin)"""
    if session.get('user_tipo') != 'admin':
        flash('Acesso negado!', 'error')
        return redirect(url_for('index'))

    conn = db.get_connection()
    cursor = conn.cursor()

    cursor.execute('SELECT * FROM usuarios ORDER BY nome')
    usuarios_lista = [dict(row) for row in cursor.fetchall()]
    conn.close()

    return render_template('usuarios/lista.html', usuarios=usuarios_lista)

@app.route('/usuarios/novo', methods=['GET', 'POST'])
def usuario_novo():
    """Cadastro de novo usuário (apenas admin)"""
    if session.get('user_tipo') != 'admin':
        flash('Acesso negado!', 'error')
        return redirect(url_for('index'))

    if request.method == 'POST':
        nome = request.form['nome']
        email = request.form['email']
        senha = request.form['senha']
        tipo = request.form['tipo']

        try:
            Usuario.criar(nome, email, senha, tipo)
            flash('Usuário cadastrado com sucesso!', 'success')
            return redirect(url_for('usuarios'))
        except Exception as e:
            flash('Erro ao cadastrar usuário: ' + str(e), 'error')

    return render_template('usuarios/form.html')

# APIs
@app.route('/api/calcular-preco', methods=['POST'])
def calcular_preco():
    """API para calcular preço de venda"""
    data = request.get_json()
    preco_custo = float(data['preco_custo'])
    lucro_desejado = float(data['lucro_desejado'])
    tipo_lucro = data['tipo_lucro']

    if tipo_lucro == 'percentual':
        preco_venda = preco_custo * (1 + lucro_desejado / 100)
    else:
        preco_venda = preco_custo + lucro_desejado

    return jsonify({'preco_venda': round(preco_venda, 2)})

@app.route('/api/dashboard-stats')
def dashboard_stats():
    """API para estatísticas do dashboard"""
    conn = db.get_connection()
    cursor = conn.cursor()

    # Estatísticas básicas
    cursor.execute('SELECT COUNT(*) as total FROM produtos WHERE ativo = 1')
    total_produtos = cursor.fetchone()['total']

    cursor.execute('SELECT COUNT(*) as total FROM clientes')
    total_clientes = cursor.fetchone()['total']

    hoje = datetime.now().strftime('%Y-%m-%d')
    cursor.execute('''
        SELECT COUNT(*) as total FROM agendamentos
        WHERE DATE(data_agendamento) = ? AND status = 'agendado'
    ''', (hoje,))
    agendamentos_hoje = cursor.fetchone()['total']

    cursor.execute('''
        SELECT COUNT(*) as total FROM produtos
        WHERE estoque_atual <= estoque_minimo AND ativo = 1
    ''')
    estoque_baixo = cursor.fetchone()['total']

    conn.close()

    return jsonify({
        'total_produtos': total_produtos,
        'total_clientes': total_clientes,
        'agendamentos_hoje': agendamentos_hoje,
        'estoque_baixo': estoque_baixo,
        'timestamp': datetime.now().isoformat()
    })

@app.route('/api/alertas')
def api_alertas():
    """API para alertas do sistema"""
    conn = db.get_connection()
    cursor = conn.cursor()

    alertas = []

    # Verificar estoque baixo
    cursor.execute('''
        SELECT COUNT(*) as total FROM produtos
        WHERE estoque_atual <= estoque_minimo AND ativo = 1
    ''')
    estoque_baixo = cursor.fetchone()['total']

    if estoque_baixo > 0:
        alertas.append({
            'tipo': 'estoque',
            'titulo': 'Produtos com Estoque Baixo',
            'mensagem': f'{estoque_baixo} produto(s) estão com estoque abaixo do mínimo',
            'icone': 'exclamation-triangle',
            'cor': 'warning',
            'acao': 'Ver Relatório',
            'link': '/relatorios/estoque-baixo'
        })

    # Verificar agendamentos hoje
    hoje = datetime.now().strftime('%Y-%m-%d')
    cursor.execute('''
        SELECT COUNT(*) as total FROM agendamentos
        WHERE DATE(data_agendamento) = ? AND status IN ('agendado', 'confirmado')
    ''', (hoje,))
    agendamentos_hoje = cursor.fetchone()['total']

    if agendamentos_hoje > 0:
        alertas.append({
            'tipo': 'agendamento',
            'titulo': 'Agendamentos Hoje',
            'mensagem': f'{agendamentos_hoje} agendamento(s) para hoje',
            'icone': 'calendar-check',
            'cor': 'info',
            'acao': 'Ver Agendamentos',
            'link': '/agendamentos'
        })

    conn.close()

    return jsonify({'alertas': alertas})

# Rotas de Backup (apenas para admin)
@app.route('/admin/backup')
def backup_dashboard():
    """Dashboard de backup (apenas admin)"""
    if session.get('user_tipo') != 'admin':
        flash('Acesso negado!', 'error')
        return redirect(url_for('index'))

    backup_system = BackupSystem()
    backups = backup_system.list_backups()

    return render_template('admin/backup.html', backups=backups)

@app.route('/admin/backup/create', methods=['POST'])
def create_backup():
    """Criar novo backup"""
    if session.get('user_tipo') != 'admin':
        return jsonify({'error': 'Acesso negado'}), 403

    backup_type = request.form.get('type', 'full')

    try:
        backup_system = BackupSystem()
        backup_path = backup_system.create_backup(backup_type)

        return jsonify({
            'success': True,
            'message': 'Backup criado com sucesso!',
            'backup_path': backup_path
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Erro ao criar backup: {str(e)}'
        }), 500

@app.route('/admin/backup/download/<filename>')
def download_backup(filename):
    """Download de arquivo de backup"""
    if session.get('user_tipo') != 'admin':
        flash('Acesso negado!', 'error')
        return redirect(url_for('index'))

    backup_dir = 'backups'
    return send_from_directory(backup_dir, filename, as_attachment=True)

if __name__ == '__main__':
    print("🚀 Iniciando Saúde Flex...")
    print("📁 Inicializando sistema...")

    try:
        print("✅ Sistema inicializado")
        print("🌐 Iniciando servidor Flask...")
        print(f"📍 Acesse: http://localhost:{AppConfig.PORT}")
        print("🔑 Login: <EMAIL> / admin123")
        print("-" * 50)

        # Abrir navegador automaticamente
        import webbrowser
        import threading
        import time

        def abrir_navegador():
            time.sleep(3)
            webbrowser.open(f'http://localhost:{AppConfig.PORT}')

        threading.Thread(target=abrir_navegador, daemon=True).start()

        app.run(debug=False, host=AppConfig.HOST, port=AppConfig.PORT)

    except Exception as e:
        print(f"❌ Erro ao iniciar: {e}")
        import traceback
        traceback.print_exc()
        input("Pressione Enter para sair...")
