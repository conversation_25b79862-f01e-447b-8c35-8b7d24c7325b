{% extends "base.html" %}

{% block title %}Clientes - Saúde Flex{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-users me-2 text-primary"></i>
                Clientes
            </h1>
            <a href="{{ url_for('cliente_novo') }}" class="btn btn-primary">
                <i class="fas fa-user-plus me-2"></i>
                Novo Cliente
            </a>
        </div>
    </div>
</div>

<!-- Filtros -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="filtro-nome">Nome do Cliente</label>
                            <input type="text" class="form-control" id="filtro-nome" placeholder="Buscar por nome...">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="filtro-cidade">Cidade</label>
                            <input type="text" class="form-control" id="filtro-cidade" placeholder="Buscar por cidade...">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="filtro-telefone">Telefone</label>
                            <input type="text" class="form-control" id="filtro-telefone" placeholder="Buscar por telefone...">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label>&nbsp;</label>
                            <button type="button" class="btn btn-outline-secondary w-100" onclick="limparFiltros()">
                                <i class="fas fa-eraser me-1"></i>
                                Limpar
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Lista de Clientes -->
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-list me-2"></i>
                    Lista de Clientes ({{ clientes|length }})
                </h6>
            </div>
            <div class="card-body">
                {% if clientes %}
                <div class="table-responsive">
                    <table class="table table-hover" id="tabela-clientes">
                        <thead>
                            <tr>
                                <th>Nome</th>
                                <th>Telefone</th>
                                <th>Email</th>
                                <th>Cidade/UF</th>
                                <th>Data Cadastro</th>
                                <th>Ações</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for cliente in clientes %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center me-3" 
                                             style="width: 40px; height: 40px;">
                                            <i class="fas fa-user text-white"></i>
                                        </div>
                                        <div>
                                            <strong>{{ cliente.nome }}</strong>
                                            {% if cliente.logradouro %}
                                            <br><small class="text-muted">{{ cliente.logradouro }}{% if cliente.numero %}, {{ cliente.numero }}{% endif %}</small>
                                            {% endif %}
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <i class="fas fa-phone me-2 text-muted"></i>
                                    <strong>{{ cliente.telefone }}</strong>
                                </td>
                                <td>
                                    {% if cliente.email %}
                                    <i class="fas fa-envelope me-2 text-muted"></i>
                                    {{ cliente.email }}
                                    {% else %}
                                    <span class="text-muted">Não informado</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if cliente.cidade %}
                                    <i class="fas fa-map-marker-alt me-2 text-muted"></i>
                                    {{ cliente.cidade }}{% if cliente.uf %}/{{ cliente.uf }}{% endif %}
                                    {% else %}
                                    <span class="text-muted">Não informado</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <i class="fas fa-calendar me-2 text-muted"></i>
                                    <span id="data-{{ cliente.id }}">{{ cliente.data_criacao }}</span>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-sm btn-outline-primary" 
                                                onclick="verCliente({{ cliente.id }})" title="Ver Detalhes">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-warning" 
                                                onclick="editarCliente({{ cliente.id }})" title="Editar">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-info" 
                                                onclick="agendarVisita({{ cliente.id }})" title="Agendar Visita">
                                            <i class="fas fa-calendar-plus"></i>
                                        </button>
                                        {% if usuario_logado.tipo in ['admin', 'gerente'] %}
                                        <button type="button" class="btn btn-sm btn-outline-danger" 
                                                onclick="excluirCliente({{ cliente.id }})" title="Excluir">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">Nenhum cliente cadastrado</h5>
                    <p class="text-muted">Comece cadastrando seu primeiro cliente.</p>
                    <a href="{{ url_for('cliente_novo') }}" class="btn btn-primary">
                        <i class="fas fa-user-plus me-2"></i>
                        Cadastrar Primeiro Cliente
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Modal de Detalhes do Cliente -->
<div class="modal fade" id="modalDetalhesCliente" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-user me-2"></i>
                    Detalhes do Cliente
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="conteudo-detalhes-cliente">
                <!-- Conteúdo será carregado via JavaScript -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
                <button type="button" class="btn btn-warning" onclick="editarClienteModal()">
                    <i class="fas fa-edit me-2"></i>
                    Editar
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let clienteAtual = null;

// Filtros
document.getElementById('filtro-nome').addEventListener('input', filtrarClientes);
document.getElementById('filtro-cidade').addEventListener('input', filtrarClientes);
document.getElementById('filtro-telefone').addEventListener('input', filtrarClientes);

function filtrarClientes() {
    const nome = document.getElementById('filtro-nome').value.toLowerCase();
    const cidade = document.getElementById('filtro-cidade').value.toLowerCase();
    const telefone = document.getElementById('filtro-telefone').value.toLowerCase();
    
    const linhas = document.querySelectorAll('#tabela-clientes tbody tr');
    
    linhas.forEach(linha => {
        const nomeCliente = linha.querySelector('td:first-child strong').textContent.toLowerCase();
        const cidadeCliente = linha.querySelector('td:nth-child(4)').textContent.toLowerCase();
        const telefoneCliente = linha.querySelector('td:nth-child(2)').textContent.toLowerCase();
        
        let mostrar = true;
        
        if (nome && !nomeCliente.includes(nome)) {
            mostrar = false;
        }
        
        if (cidade && !cidadeCliente.includes(cidade)) {
            mostrar = false;
        }
        
        if (telefone && !telefoneCliente.includes(telefone)) {
            mostrar = false;
        }
        
        linha.style.display = mostrar ? '' : 'none';
    });
}

function limparFiltros() {
    document.getElementById('filtro-nome').value = '';
    document.getElementById('filtro-cidade').value = '';
    document.getElementById('filtro-telefone').value = '';
    filtrarClientes();
}

function verCliente(id) {
    clienteAtual = id;
    
    // Simular carregamento de dados do cliente
    const conteudo = `
        <div class="row">
            <div class="col-md-6">
                <h6><i class="fas fa-user me-2"></i>Informações Pessoais</h6>
                <p><strong>Nome:</strong> Cliente ${id}</p>
                <p><strong>Telefone:</strong> (11) 99999-9999</p>
                <p><strong>Email:</strong> cliente${id}@email.com</p>
            </div>
            <div class="col-md-6">
                <h6><i class="fas fa-map-marker-alt me-2"></i>Endereço</h6>
                <p><strong>Logradouro:</strong> Rua Exemplo, 123</p>
                <p><strong>Bairro:</strong> Centro</p>
                <p><strong>Cidade/UF:</strong> São Paulo/SP</p>
                <p><strong>CEP:</strong> 01234-567</p>
            </div>
        </div>
        <hr>
        <div class="row">
            <div class="col-12">
                <h6><i class="fas fa-history me-2"></i>Histórico de Agendamentos</h6>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Data</th>
                                <th>Produto</th>
                                <th>Vendedor</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>15/06/2024</td>
                                <td>Produto Exemplo</td>
                                <td>Vendedor Teste</td>
                                <td><span class="badge bg-success">Realizado</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    `;
    
    document.getElementById('conteudo-detalhes-cliente').innerHTML = conteudo;
    
    const modal = new bootstrap.Modal(document.getElementById('modalDetalhesCliente'));
    modal.show();
}

function editarCliente(id) {
    window.location.href = '/clientes/editar/' + id;
}

function editarClienteModal() {
    if (clienteAtual) {
        editarCliente(clienteAtual);
    }
}

function agendarVisita(id) {
    window.location.href = '/agendamentos/novo?cliente_id=' + id;
}

function excluirCliente(id) {
    SaudeFlex.modal.confirm(
        'Tem certeza que deseja excluir este cliente? Esta ação não pode ser desfeita.',
        function() {
            // Implementar exclusão
            SaudeFlex.notify.success('Cliente excluído com sucesso!');
            // Recarregar página ou remover linha da tabela
        }
    );
}

// Formatar datas na tabela
document.addEventListener('DOMContentLoaded', function() {
    document.querySelectorAll('[id^="data-"]').forEach(element => {
        const data = element.textContent;
        if (data) {
            element.textContent = SaudeFlex.utils.formatDate(data);
        }
    });
});
</script>
{% endblock %}
