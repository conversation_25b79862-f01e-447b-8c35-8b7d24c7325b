{% extends "base.html" %}

{% block title %}{{ 'Editar' if cliente else 'Novo' }} Cliente - Saúde Flex{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-{{ 'user-edit' if cliente else 'user-plus' }} me-2 text-primary"></i>
                {{ 'Editar' if cliente else 'Novo' }} Cliente
            </h1>
            <a href="{{ url_for('clientes') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>
                Voltar
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-info-circle me-2"></i>
                    Informações do Cliente
                </h6>
            </div>
            <div class="card-body">
                <form method="POST" id="form-cliente">
                    <!-- Informações Pessoais -->
                    <div class="row">
                        <div class="col-12">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-user me-2"></i>
                                Dados Pessoais
                            </h6>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="nome" class="form-label">
                                    <i class="fas fa-user me-1"></i>
                                    Nome Completo *
                                </label>
                                <input type="text" class="form-control" id="nome" name="nome" 
                                       value="{{ cliente.nome if cliente else '' }}" required>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group mb-3">
                                <label for="telefone" class="form-label">
                                    <i class="fas fa-phone me-1"></i>
                                    Telefone *
                                </label>
                                <input type="text" class="form-control" id="telefone" name="telefone" 
                                       data-mask="phone" value="{{ cliente.telefone if cliente else '' }}" required>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group mb-3">
                                <label for="email" class="form-label">
                                    <i class="fas fa-envelope me-1"></i>
                                    Email
                                </label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="{{ cliente.email if cliente else '' }}">
                            </div>
                        </div>
                    </div>

                    <!-- Endereço -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-map-marker-alt me-2"></i>
                                Endereço
                            </h6>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-2">
                            <div class="form-group mb-3">
                                <label for="cep" class="form-label">
                                    <i class="fas fa-mail-bulk me-1"></i>
                                    CEP
                                </label>
                                <input type="text" class="form-control" id="cep" name="cep" 
                                       data-mask="cep" value="{{ cliente.cep if cliente else '' }}" 
                                       onblur="buscarCep()">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="logradouro" class="form-label">
                                    <i class="fas fa-road me-1"></i>
                                    Logradouro
                                </label>
                                <input type="text" class="form-control" id="logradouro" name="logradouro" 
                                       value="{{ cliente.logradouro if cliente else '' }}">
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group mb-3">
                                <label for="numero" class="form-label">
                                    <i class="fas fa-hashtag me-1"></i>
                                    Número
                                </label>
                                <input type="text" class="form-control" id="numero" name="numero" 
                                       value="{{ cliente.numero if cliente else '' }}">
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group mb-3">
                                <label>&nbsp;</label>
                                <button type="button" class="btn btn-outline-info w-100" onclick="buscarCep()">
                                    <i class="fas fa-search me-1"></i>
                                    Buscar CEP
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group mb-3">
                                <label for="bairro" class="form-label">
                                    <i class="fas fa-map me-1"></i>
                                    Bairro
                                </label>
                                <input type="text" class="form-control" id="bairro" name="bairro" 
                                       value="{{ cliente.bairro if cliente else '' }}">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="cidade" class="form-label">
                                    <i class="fas fa-city me-1"></i>
                                    Cidade
                                </label>
                                <input type="text" class="form-control" id="cidade" name="cidade" 
                                       value="{{ cliente.cidade if cliente else '' }}">
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group mb-3">
                                <label for="uf" class="form-label">
                                    <i class="fas fa-flag me-1"></i>
                                    UF
                                </label>
                                <select class="form-control" id="uf" name="uf">
                                    <option value="">Selecione</option>
                                    <option value="AC" {{ 'selected' if cliente and cliente.uf == 'AC' else '' }}>AC</option>
                                    <option value="AL" {{ 'selected' if cliente and cliente.uf == 'AL' else '' }}>AL</option>
                                    <option value="AP" {{ 'selected' if cliente and cliente.uf == 'AP' else '' }}>AP</option>
                                    <option value="AM" {{ 'selected' if cliente and cliente.uf == 'AM' else '' }}>AM</option>
                                    <option value="BA" {{ 'selected' if cliente and cliente.uf == 'BA' else '' }}>BA</option>
                                    <option value="CE" {{ 'selected' if cliente and cliente.uf == 'CE' else '' }}>CE</option>
                                    <option value="DF" {{ 'selected' if cliente and cliente.uf == 'DF' else '' }}>DF</option>
                                    <option value="ES" {{ 'selected' if cliente and cliente.uf == 'ES' else '' }}>ES</option>
                                    <option value="GO" {{ 'selected' if cliente and cliente.uf == 'GO' else '' }}>GO</option>
                                    <option value="MA" {{ 'selected' if cliente and cliente.uf == 'MA' else '' }}>MA</option>
                                    <option value="MT" {{ 'selected' if cliente and cliente.uf == 'MT' else '' }}>MT</option>
                                    <option value="MS" {{ 'selected' if cliente and cliente.uf == 'MS' else '' }}>MS</option>
                                    <option value="MG" {{ 'selected' if cliente and cliente.uf == 'MG' else '' }}>MG</option>
                                    <option value="PA" {{ 'selected' if cliente and cliente.uf == 'PA' else '' }}>PA</option>
                                    <option value="PB" {{ 'selected' if cliente and cliente.uf == 'PB' else '' }}>PB</option>
                                    <option value="PR" {{ 'selected' if cliente and cliente.uf == 'PR' else '' }}>PR</option>
                                    <option value="PE" {{ 'selected' if cliente and cliente.uf == 'PE' else '' }}>PE</option>
                                    <option value="PI" {{ 'selected' if cliente and cliente.uf == 'PI' else '' }}>PI</option>
                                    <option value="RJ" {{ 'selected' if cliente and cliente.uf == 'RJ' else '' }}>RJ</option>
                                    <option value="RN" {{ 'selected' if cliente and cliente.uf == 'RN' else '' }}>RN</option>
                                    <option value="RS" {{ 'selected' if cliente and cliente.uf == 'RS' else '' }}>RS</option>
                                    <option value="RO" {{ 'selected' if cliente and cliente.uf == 'RO' else '' }}>RO</option>
                                    <option value="RR" {{ 'selected' if cliente and cliente.uf == 'RR' else '' }}>RR</option>
                                    <option value="SC" {{ 'selected' if cliente and cliente.uf == 'SC' else '' }}>SC</option>
                                    <option value="SP" {{ 'selected' if cliente and cliente.uf == 'SP' else '' }}>SP</option>
                                    <option value="SE" {{ 'selected' if cliente and cliente.uf == 'SE' else '' }}>SE</option>
                                    <option value="TO" {{ 'selected' if cliente and cliente.uf == 'TO' else '' }}>TO</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between mt-4">
                        <a href="{{ url_for('clientes') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>
                            Cancelar
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            {{ 'Atualizar' if cliente else 'Cadastrar' }} Cliente
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- Preview do Cliente -->
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-eye me-2"></i>
                    Preview do Cliente
                </h6>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center mx-auto" 
                         style="width: 80px; height: 80px;">
                        <i class="fas fa-user fa-2x text-white"></i>
                    </div>
                </div>
                
                <h5 id="preview-nome" class="text-center">{{ cliente.nome if cliente else 'Nome do Cliente' }}</h5>
                
                <div class="mt-3">
                    <p class="mb-2">
                        <i class="fas fa-phone text-muted me-2"></i>
                        <span id="preview-telefone">{{ cliente.telefone if cliente else 'Telefone' }}</span>
                    </p>
                    <p class="mb-2">
                        <i class="fas fa-envelope text-muted me-2"></i>
                        <span id="preview-email">{{ cliente.email if cliente else 'Email' }}</span>
                    </p>
                    <p class="mb-2">
                        <i class="fas fa-map-marker-alt text-muted me-2"></i>
                        <span id="preview-endereco">
                            {% if cliente %}
                                {{ cliente.logradouro }}{% if cliente.numero %}, {{ cliente.numero }}{% endif %}
                                {% if cliente.bairro %}<br>{{ cliente.bairro }}{% endif %}
                                {% if cliente.cidade %}<br>{{ cliente.cidade }}{% if cliente.uf %}/{{ cliente.uf }}{% endif %}{% endif %}
                            {% else %}
                                Endereço
                            {% endif %}
                        </span>
                    </p>
                </div>
            </div>
        </div>

        <!-- Dicas -->
        <div class="card shadow mt-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-info">
                    <i class="fas fa-lightbulb me-2"></i>
                    Dicas
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        <small>Nome e telefone são obrigatórios</small>
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        <small>Use o CEP para preencher o endereço automaticamente</small>
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        <small>Email facilita o contato e envio de comprovantes</small>
                    </li>
                    <li>
                        <i class="fas fa-check text-success me-2"></i>
                        <small>Endereço completo ajuda no agendamento de visitas</small>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Atualizar preview em tempo real
document.getElementById('nome').addEventListener('input', function() {
    document.getElementById('preview-nome').textContent = this.value || 'Nome do Cliente';
});

document.getElementById('telefone').addEventListener('input', function() {
    document.getElementById('preview-telefone').textContent = this.value || 'Telefone';
});

document.getElementById('email').addEventListener('input', function() {
    document.getElementById('preview-email').textContent = this.value || 'Email';
});

// Atualizar endereço no preview
function atualizarPreviewEndereco() {
    const logradouro = document.getElementById('logradouro').value;
    const numero = document.getElementById('numero').value;
    const bairro = document.getElementById('bairro').value;
    const cidade = document.getElementById('cidade').value;
    const uf = document.getElementById('uf').value;
    
    let endereco = '';
    
    if (logradouro) {
        endereco += logradouro;
        if (numero) endereco += ', ' + numero;
    }
    
    if (bairro) {
        if (endereco) endereco += '<br>';
        endereco += bairro;
    }
    
    if (cidade) {
        if (endereco) endereco += '<br>';
        endereco += cidade;
        if (uf) endereco += '/' + uf;
    }
    
    document.getElementById('preview-endereco').innerHTML = endereco || 'Endereço';
}

// Event listeners para endereço
['logradouro', 'numero', 'bairro', 'cidade', 'uf'].forEach(campo => {
    document.getElementById(campo).addEventListener('input', atualizarPreviewEndereco);
});

// Buscar CEP
async function buscarCep() {
    const cep = document.getElementById('cep').value.replace(/\D/g, '');
    
    if (cep.length !== 8) {
        SaudeFlex.notify.warning('CEP deve ter 8 dígitos.');
        return;
    }
    
    const btn = event.target;
    SaudeFlex.loading.show(btn);
    
    try {
        const response = await fetch(`https://viacep.com.br/ws/${cep}/json/`);
        const data = await response.json();
        
        if (data.erro) {
            SaudeFlex.notify.error('CEP não encontrado.');
            return;
        }
        
        document.getElementById('logradouro').value = data.logradouro || '';
        document.getElementById('bairro').value = data.bairro || '';
        document.getElementById('cidade').value = data.localidade || '';
        document.getElementById('uf').value = data.uf || '';
        
        atualizarPreviewEndereco();
        
        SaudeFlex.notify.success('Endereço preenchido automaticamente!');
        
        // Focar no campo número
        document.getElementById('numero').focus();
        
    } catch (error) {
        SaudeFlex.notify.error('Erro ao buscar CEP. Tente novamente.');
    } finally {
        SaudeFlex.loading.hide(btn);
    }
}

// Validação do formulário
document.getElementById('form-cliente').addEventListener('submit', function(e) {
    const nome = document.getElementById('nome').value.trim();
    const telefone = document.getElementById('telefone').value.trim();
    
    if (!nome) {
        e.preventDefault();
        SaudeFlex.notify.error('Nome é obrigatório.');
        document.getElementById('nome').focus();
        return;
    }
    
    if (!telefone) {
        e.preventDefault();
        SaudeFlex.notify.error('Telefone é obrigatório.');
        document.getElementById('telefone').focus();
        return;
    }
});
</script>
{% endblock %}
