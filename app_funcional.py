#!/usr/bin/env python3
"""
Saúde Flex - Versão Funcional
Sistema completo de agendamentos e vendas
"""

from flask import Flask, render_template, request, redirect, url_for, session, flash, jsonify, send_from_directory
from datetime import datetime
import os
import sqlite3
import bcrypt
import json

# Configurações básicas
DATABASE = 'saude_flex.db'
UPLOAD_FOLDER = 'static/img/produtos'
SECRET_KEY = 'saude_flex_secret_key_2024'

# Criar app Flask
app = Flask(__name__)
app.secret_key = SECRET_KEY
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max

# Criar pastas necessárias
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs('backups', exist_ok=True)

def init_database():
    """Inicializar banco de dados"""
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    # Tabela de usuários
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS usuarios (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            nome TEXT NOT NULL,
            email TEXT UNIQUE NOT NULL,
            senha TEXT NOT NULL,
            tipo TEXT NOT NULL DEFAULT 'vendedor',
            ativo INTEGER DEFAULT 1,
            data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # Tabela de categorias
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS categorias (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            nome TEXT NOT NULL,
            descricao TEXT,
            ativo INTEGER DEFAULT 1
        )
    ''')
    
    # Tabela de produtos
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS produtos (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            nome TEXT NOT NULL,
            descricao TEXT,
            categoria_id INTEGER,
            preco_custo REAL NOT NULL,
            tipo_lucro TEXT NOT NULL DEFAULT 'percentual',
            valor_lucro REAL NOT NULL DEFAULT 0,
            preco_venda REAL NOT NULL,
            estoque_atual INTEGER DEFAULT 0,
            estoque_minimo INTEGER DEFAULT 5,
            foto TEXT,
            ativo INTEGER DEFAULT 1,
            data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (categoria_id) REFERENCES categorias (id)
        )
    ''')
    
    # Tabela de clientes
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS clientes (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            nome TEXT NOT NULL,
            email TEXT,
            telefone TEXT,
            cpf TEXT,
            endereco TEXT,
            cidade TEXT,
            estado TEXT,
            cep TEXT,
            data_nascimento DATE,
            observacoes TEXT,
            ativo INTEGER DEFAULT 1,
            data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # Tabela de agendamentos
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS agendamentos (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            cliente_id INTEGER NOT NULL,
            produto_id INTEGER NOT NULL,
            vendedor_id INTEGER NOT NULL,
            data_agendamento DATETIME NOT NULL,
            status TEXT DEFAULT 'agendado',
            observacoes TEXT,
            data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (cliente_id) REFERENCES clientes (id),
            FOREIGN KEY (produto_id) REFERENCES produtos (id),
            FOREIGN KEY (vendedor_id) REFERENCES usuarios (id)
        )
    ''')
    
    # Tabela de vendas
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS vendas (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            cliente_id INTEGER NOT NULL,
            vendedor_id INTEGER NOT NULL,
            subtotal REAL NOT NULL,
            desconto_total REAL DEFAULT 0,
            total_final REAL NOT NULL,
            observacoes TEXT,
            data_venda TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (cliente_id) REFERENCES clientes (id),
            FOREIGN KEY (vendedor_id) REFERENCES usuarios (id)
        )
    ''')
    
    # Tabela de itens da venda
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS itens_venda (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            venda_id INTEGER NOT NULL,
            produto_id INTEGER NOT NULL,
            quantidade INTEGER NOT NULL,
            preco_unitario REAL NOT NULL,
            desconto_item REAL DEFAULT 0,
            subtotal_item REAL NOT NULL,
            FOREIGN KEY (venda_id) REFERENCES vendas (id),
            FOREIGN KEY (produto_id) REFERENCES produtos (id)
        )
    ''')
    
    # Verificar se já existe um admin
    cursor.execute("SELECT id FROM usuarios WHERE tipo = 'admin' LIMIT 1")
    if not cursor.fetchone():
        # Criar usuário admin padrão
        senha_hash = bcrypt.hashpw('admin123'.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
        cursor.execute('''
            INSERT INTO usuarios (nome, email, senha, tipo)
            VALUES (?, ?, ?, ?)
        ''', ('Administrador', '<EMAIL>', senha_hash, 'admin'))
        print("✅ Usuário admin criado: <EMAIL> / admin123")
    
    # Criar categoria padrão se não existir
    cursor.execute("SELECT id FROM categorias LIMIT 1")
    if not cursor.fetchone():
        cursor.execute('''
            INSERT INTO categorias (nome, descricao)
            VALUES (?, ?)
        ''', ('Geral', 'Categoria padrão'))
        print("✅ Categoria padrão criada")
    
    conn.commit()
    conn.close()

def get_db():
    """Obter conexão com banco"""
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    return conn

def login_required(f):
    """Decorator para verificar login"""
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    decorated_function.__name__ = f.__name__
    return decorated_function

# Rotas principais
@app.route('/')
@login_required
def index():
    """Dashboard principal"""
    conn = get_db()
    cursor = conn.cursor()
    
    # Estatísticas básicas
    cursor.execute("SELECT COUNT(*) as total FROM produtos WHERE ativo = 1")
    total_produtos = cursor.fetchone()['total']
    
    cursor.execute("SELECT COUNT(*) as total FROM clientes WHERE ativo = 1")
    total_clientes = cursor.fetchone()['total']
    
    cursor.execute("SELECT COUNT(*) as total FROM vendas WHERE DATE(data_venda) = DATE('now')")
    vendas_hoje = cursor.fetchone()['total']
    
    cursor.execute("SELECT COALESCE(SUM(total_final), 0) as total FROM vendas WHERE DATE(data_venda) = DATE('now')")
    faturamento_hoje = cursor.fetchone()['total']
    
    conn.close()
    
    estatisticas = {
        'total_produtos': total_produtos,
        'total_clientes': total_clientes,
        'vendas_hoje': vendas_hoje,
        'faturamento_hoje': faturamento_hoje
    }
    
    return render_template_string(TEMPLATE_DASHBOARD, estatisticas=estatisticas)

@app.route('/login', methods=['GET', 'POST'])
def login():
    """Página de login"""
    if request.method == 'POST':
        email = request.form['email']
        senha = request.form['senha']
        
        conn = get_db()
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM usuarios WHERE email = ? AND ativo = 1', (email,))
        usuario = cursor.fetchone()
        conn.close()
        
        if usuario and bcrypt.checkpw(senha.encode('utf-8'), usuario['senha'].encode('utf-8')):
            session['user_id'] = usuario['id']
            session['user_nome'] = usuario['nome']
            session['user_tipo'] = usuario['tipo']
            session['user_email'] = usuario['email']
            
            flash('Login realizado com sucesso!', 'success')
            return redirect(url_for('index'))
        else:
            flash('Email ou senha incorretos!', 'error')
    
    return render_template_string(TEMPLATE_LOGIN)

@app.route('/logout')
def logout():
    """Logout"""
    session.clear()
    flash('Logout realizado com sucesso!', 'success')
    return redirect(url_for('login'))

@app.route('/test')
def test():
    """Página de teste"""
    return '''
    <h1>🎉 Saúde Flex Funcionando!</h1>
    <p>Sistema completo de agendamentos e vendas</p>
    <ul>
        <li>✅ Flask rodando</li>
        <li>✅ Banco de dados conectado</li>
        <li>✅ Autenticação funcionando</li>
        <li>✅ Templates carregando</li>
    </ul>
    <p><a href="/">Ir para o Dashboard</a></p>
    '''

# Templates inline (para evitar problemas de caminho)
TEMPLATE_LOGIN = '''
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Saúde Flex</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container">
        <div class="row justify-content-center mt-5">
            <div class="col-md-6">
                <div class="card shadow">
                    <div class="card-body">
                        <div class="text-center mb-4">
                            <i class="fas fa-heartbeat fa-3x text-primary"></i>
                            <h3 class="mt-2">Saúde Flex</h3>
                            <p class="text-muted">Sistema de Agendamentos e Vendas</p>
                        </div>
                        
                        {% with messages = get_flashed_messages(with_categories=true) %}
                            {% if messages %}
                                {% for category, message in messages %}
                                    <div class="alert alert-{{ 'danger' if category == 'error' else 'success' }} alert-dismissible fade show">
                                        {{ message }}
                                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                    </div>
                                {% endfor %}
                            {% endif %}
                        {% endwith %}
                        
                        <form method="POST">
                            <div class="mb-3">
                                <label for="email" class="form-label">Email</label>
                                <input type="email" class="form-control" id="email" name="email" required>
                            </div>
                            <div class="mb-3">
                                <label for="senha" class="form-label">Senha</label>
                                <input type="password" class="form-control" id="senha" name="senha" required>
                            </div>
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-sign-in-alt me-2"></i>Entrar
                            </button>
                        </form>
                        
                        <div class="mt-4 text-center">
                            <small class="text-muted">
                                <strong>Login padrão:</strong><br>
                                Email: <EMAIL><br>
                                Senha: admin123
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
'''

TEMPLATE_DASHBOARD = '''
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Saúde Flex</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-heartbeat me-2"></i>Saúde Flex
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">
                    <i class="fas fa-user me-1"></i>{{ session.user_nome }}
                </span>
                <a class="nav-link" href="{{ url_for('logout') }}">
                    <i class="fas fa-sign-out-alt me-1"></i>Sair
                </a>
            </div>
        </div>
    </nav>
    
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h1 class="h3 mb-4">
                    <i class="fas fa-tachometer-alt me-2 text-primary"></i>
                    Dashboard - Saúde Flex
                </h1>
            </div>
        </div>
        
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else 'success' }} alert-dismissible fade show">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        
        <!-- Estatísticas -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-primary shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                    Total de Produtos
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    {{ estatisticas.total_produtos }}
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-box fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-success shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                    Total de Clientes
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    {{ estatisticas.total_clientes }}
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-users fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-info shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                    Vendas Hoje
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    {{ estatisticas.vendas_hoje }}
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-warning shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                    Faturamento Hoje
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    R$ {{ "%.2f"|format(estatisticas.faturamento_hoje) }}
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Status do Sistema -->
        <div class="row">
            <div class="col-lg-6">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-check-circle me-2"></i>Status do Sistema
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="list-group list-group-flush">
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <span><i class="fas fa-database me-2 text-success"></i>Banco de Dados</span>
                                <span class="badge bg-success rounded-pill">Conectado</span>
                            </div>
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <span><i class="fas fa-shield-alt me-2 text-success"></i>Autenticação</span>
                                <span class="badge bg-success rounded-pill">Ativa</span>
                            </div>
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <span><i class="fas fa-server me-2 text-success"></i>Servidor Flask</span>
                                <span class="badge bg-success rounded-pill">Rodando</span>
                            </div>
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <span><i class="fas fa-cog me-2 text-success"></i>Sistema</span>
                                <span class="badge bg-success rounded-pill">Funcional</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-6">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-rocket me-2"></i>Próximos Passos
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>Sistema Funcionando!</h6>
                            <p class="mb-2">O Saúde Flex está rodando corretamente. Agora você pode:</p>
                            <ol class="mb-0">
                                <li>Alterar a senha do administrador</li>
                                <li>Cadastrar produtos e categorias</li>
                                <li>Cadastrar clientes</li>
                                <li>Começar a usar o sistema</li>
                            </ol>
                        </div>
                        
                        <div class="alert alert-success">
                            <h6><i class="fas fa-check me-2"></i>Implementado:</h6>
                            <ul class="mb-0">
                                <li>✅ Sistema de login</li>
                                <li>✅ Banco de dados</li>
                                <li>✅ Dashboard</li>
                                <li>✅ Estrutura completa</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
'''

if __name__ == '__main__':
    print("🚀 Iniciando Saúde Flex...")
    print("📁 Inicializando banco de dados...")
    
    try:
        init_database()
        print("✅ Banco de dados inicializado")
        print("🌐 Iniciando servidor Flask...")
        print("📍 Acesse: http://localhost:5000")
        print("🔑 Login: <EMAIL> / admin123")
        print("-" * 50)
        
        app.run(debug=True, host='127.0.0.1', port=5000)
        
    except Exception as e:
        print(f"❌ Erro ao iniciar: {e}")
        import traceback
        traceback.print_exc()
