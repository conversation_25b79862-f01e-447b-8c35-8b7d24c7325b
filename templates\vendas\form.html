{% extends "base.html" %}

{% block title %}Nova Venda - Saúde Flex{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-shopping-cart me-2 text-primary"></i>
                Nova Venda
            </h1>
            <a href="{{ url_for('vendas') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>
                Voltar
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <!-- Seleção do Cliente -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-user me-2"></i>
                    1. Selecionar Cliente
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <select class="form-control" id="cliente_id" required>
                            <option value="">Selecione um cliente</option>
                            {% for cliente in clientes %}
                            <option value="{{ cliente.id }}" 
                                    data-nome="{{ cliente.nome }}"
                                    data-telefone="{{ cliente.telefone }}">
                                {{ cliente.nome }} - {{ cliente.telefone }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-4">
                        <a href="{{ url_for('cliente_novo') }}" target="_blank" class="btn btn-outline-primary w-100">
                            <i class="fas fa-user-plus me-2"></i>
                            Novo Cliente
                        </a>
                    </div>
                </div>
                <div id="info-cliente" class="mt-3" style="display: none;">
                    <div class="alert alert-info">
                        <strong id="nome-cliente"></strong><br>
                        <i class="fas fa-phone me-1"></i>
                        <span id="telefone-cliente"></span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Seleção de Produtos -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-box me-2"></i>
                    2. Adicionar Produtos
                </h6>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-5">
                        <select class="form-control" id="produto_select">
                            <option value="">Selecione um produto</option>
                            {% for produto in produtos %}
                            <option value="{{ produto.id }}" 
                                    data-nome="{{ produto.nome }}"
                                    data-preco="{{ produto.preco_venda }}"
                                    data-estoque="{{ produto.estoque_atual }}">
                                {{ produto.nome }} - R$ {{ "%.2f"|format(produto.preco_venda) }} (Est: {{ produto.estoque_atual }})
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <input type="number" class="form-control" id="quantidade_produto" placeholder="Qtd" min="1" value="1">
                    </div>
                    <div class="col-md-3">
                        <input type="number" class="form-control" id="desconto_produto" placeholder="Desconto (R$)" min="0" step="0.01">
                    </div>
                    <div class="col-md-2">
                        <button type="button" class="btn btn-success w-100" onclick="adicionarProduto()">
                            <i class="fas fa-plus"></i>
                        </button>
                    </div>
                </div>

                <!-- Lista de Produtos no Carrinho -->
                <div class="table-responsive">
                    <table class="table table-hover" id="tabela-carrinho">
                        <thead>
                            <tr>
                                <th>Produto</th>
                                <th>Qtd</th>
                                <th>Preço Unit.</th>
                                <th>Desconto</th>
                                <th>Subtotal</th>
                                <th>Ações</th>
                            </tr>
                        </thead>
                        <tbody id="carrinho-itens">
                            <tr id="carrinho-vazio">
                                <td colspan="6" class="text-center text-muted py-4">
                                    <i class="fas fa-shopping-cart fa-2x mb-2"></i><br>
                                    Nenhum produto adicionado
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Finalização da Venda -->
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-calculator me-2"></i>
                    3. Finalizar Venda
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label for="desconto_total" class="form-label">
                                <i class="fas fa-percentage me-1"></i>
                                Desconto Total (R$)
                            </label>
                            <input type="number" class="form-control" id="desconto_total" min="0" step="0.01" value="0" onchange="calcularTotal()">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label for="observacoes_venda" class="form-label">
                                <i class="fas fa-sticky-note me-1"></i>
                                Observações
                            </label>
                            <input type="text" class="form-control" id="observacoes_venda" placeholder="Observações da venda...">
                        </div>
                    </div>
                </div>

                <div class="d-flex justify-content-between">
                    <a href="{{ url_for('vendas') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-2"></i>
                        Cancelar
                    </a>
                    <button type="button" class="btn btn-success btn-lg" onclick="finalizarVenda()" id="btn-finalizar" disabled>
                        <i class="fas fa-check me-2"></i>
                        Finalizar Venda
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- Resumo da Venda -->
        <div class="card shadow sticky-top">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-success">
                    <i class="fas fa-receipt me-2"></i>
                    Resumo da Venda
                </h6>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between mb-2">
                    <span>Subtotal:</span>
                    <span id="resumo-subtotal">R$ 0,00</span>
                </div>
                <div class="d-flex justify-content-between mb-2">
                    <span>Desconto nos Itens:</span>
                    <span id="resumo-desconto-itens" class="text-warning">-R$ 0,00</span>
                </div>
                <div class="d-flex justify-content-between mb-2">
                    <span>Desconto Total:</span>
                    <span id="resumo-desconto-total" class="text-warning">-R$ 0,00</span>
                </div>
                <hr>
                <div class="d-flex justify-content-between">
                    <strong>Total Final:</strong>
                    <strong id="resumo-total" class="text-success h5">R$ 0,00</strong>
                </div>

                <div class="mt-4">
                    <h6 class="text-muted">Itens no Carrinho:</h6>
                    <div id="resumo-itens">
                        <p class="text-muted">Nenhum item adicionado</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Dicas -->
        <div class="card shadow mt-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-info">
                    <i class="fas fa-lightbulb me-2"></i>
                    Dicas
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        <small>Verifique o estoque antes de adicionar produtos</small>
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        <small>Descontos podem ser aplicados por item ou no total</small>
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        <small>O comprovante será gerado automaticamente</small>
                    </li>
                    <li>
                        <i class="fas fa-check text-success me-2"></i>
                        <small>O estoque será atualizado automaticamente</small>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let carrinho = [];
let carrinhoIndex = 0;

// Seleção do cliente
document.getElementById('cliente_id').addEventListener('change', function() {
    const option = this.options[this.selectedIndex];
    const infoCliente = document.getElementById('info-cliente');
    
    if (option.value) {
        document.getElementById('nome-cliente').textContent = option.dataset.nome;
        document.getElementById('telefone-cliente').textContent = option.dataset.telefone;
        infoCliente.style.display = 'block';
    } else {
        infoCliente.style.display = 'none';
    }
    
    verificarPodeFinalizarVenda();
});

// Adicionar produto ao carrinho
function adicionarProduto() {
    const produtoSelect = document.getElementById('produto_select');
    const quantidade = parseInt(document.getElementById('quantidade_produto').value) || 1;
    const desconto = parseFloat(document.getElementById('desconto_produto').value) || 0;
    
    if (!produtoSelect.value) {
        SaudeFlex.notify.error('Selecione um produto.');
        return;
    }
    
    const option = produtoSelect.options[produtoSelect.selectedIndex];
    const estoque = parseInt(option.dataset.estoque);
    
    if (quantidade > estoque) {
        SaudeFlex.notify.error(`Estoque insuficiente. Disponível: ${estoque}`);
        return;
    }
    
    const produto = {
        id: carrinhoIndex++,
        produto_id: option.value,
        nome: option.dataset.nome,
        preco: parseFloat(option.dataset.preco),
        quantidade: quantidade,
        desconto: desconto,
        subtotal: (parseFloat(option.dataset.preco) * quantidade) - desconto
    };
    
    carrinho.push(produto);
    atualizarTabelaCarrinho();
    atualizarResumo();
    
    // Limpar campos
    produtoSelect.value = '';
    document.getElementById('quantidade_produto').value = '1';
    document.getElementById('desconto_produto').value = '';
    
    SaudeFlex.notify.success('Produto adicionado ao carrinho!');
}

// Remover produto do carrinho
function removerProduto(id) {
    carrinho = carrinho.filter(item => item.id !== id);
    atualizarTabelaCarrinho();
    atualizarResumo();
    SaudeFlex.notify.info('Produto removido do carrinho.');
}

// Atualizar tabela do carrinho
function atualizarTabelaCarrinho() {
    const tbody = document.getElementById('carrinho-itens');
    const carrinhoVazio = document.getElementById('carrinho-vazio');
    
    if (carrinho.length === 0) {
        carrinhoVazio.style.display = 'table-row';
        return;
    }
    
    carrinhoVazio.style.display = 'none';
    
    const itensExistentes = tbody.querySelectorAll('tr:not(#carrinho-vazio)');
    itensExistentes.forEach(tr => tr.remove());
    
    carrinho.forEach(item => {
        const tr = document.createElement('tr');
        tr.innerHTML = `
            <td>${item.nome}</td>
            <td>${item.quantidade}</td>
            <td>R$ ${item.preco.toFixed(2)}</td>
            <td>R$ ${item.desconto.toFixed(2)}</td>
            <td><strong>R$ ${item.subtotal.toFixed(2)}</strong></td>
            <td>
                <button type="button" class="btn btn-sm btn-outline-danger" onclick="removerProduto(${item.id})">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        `;
        tbody.appendChild(tr);
    });
}

// Atualizar resumo
function atualizarResumo() {
    const subtotal = carrinho.reduce((sum, item) => sum + (item.preco * item.quantidade), 0);
    const descontoItens = carrinho.reduce((sum, item) => sum + item.desconto, 0);
    const descontoTotal = parseFloat(document.getElementById('desconto_total').value) || 0;
    const total = subtotal - descontoItens - descontoTotal;
    
    document.getElementById('resumo-subtotal').textContent = `R$ ${subtotal.toFixed(2)}`;
    document.getElementById('resumo-desconto-itens').textContent = `-R$ ${descontoItens.toFixed(2)}`;
    document.getElementById('resumo-desconto-total').textContent = `-R$ ${descontoTotal.toFixed(2)}`;
    document.getElementById('resumo-total').textContent = `R$ ${Math.max(0, total).toFixed(2)}`;
    
    // Atualizar lista de itens no resumo
    const resumoItens = document.getElementById('resumo-itens');
    if (carrinho.length === 0) {
        resumoItens.innerHTML = '<p class="text-muted">Nenhum item adicionado</p>';
    } else {
        resumoItens.innerHTML = carrinho.map(item => 
            `<small class="d-block">${item.quantidade}x ${item.nome}</small>`
        ).join('');
    }
    
    verificarPodeFinalizarVenda();
}

// Calcular total quando desconto total muda
function calcularTotal() {
    atualizarResumo();
}

// Verificar se pode finalizar venda
function verificarPodeFinalizarVenda() {
    const clienteSelecionado = document.getElementById('cliente_id').value;
    const temItens = carrinho.length > 0;
    const btnFinalizar = document.getElementById('btn-finalizar');
    
    btnFinalizar.disabled = !(clienteSelecionado && temItens);
}

// Finalizar venda
function finalizarVenda() {
    const clienteId = document.getElementById('cliente_id').value;
    const descontoTotal = parseFloat(document.getElementById('desconto_total').value) || 0;
    const observacoes = document.getElementById('observacoes_venda').value;
    
    if (!clienteId || carrinho.length === 0) {
        SaudeFlex.notify.error('Selecione um cliente e adicione produtos.');
        return;
    }
    
    const venda = {
        cliente_id: clienteId,
        desconto_total: descontoTotal,
        observacoes: observacoes,
        itens: carrinho.map(item => ({
            produto_id: item.produto_id,
            quantidade: item.quantidade,
            preco_unitario: item.preco,
            desconto_item: item.desconto
        }))
    };
    
    // Simular envio da venda
    SaudeFlex.loading.show('#btn-finalizar');
    
    setTimeout(() => {
        SaudeFlex.loading.hide('#btn-finalizar');
        SaudeFlex.notify.success('Venda realizada com sucesso!');
        
        // Redirecionar para lista de vendas
        setTimeout(() => {
            window.location.href = '/vendas';
        }, 2000);
    }, 2000);
}

// Event listeners
document.getElementById('desconto_total').addEventListener('input', calcularTotal);
</script>
{% endblock %}
