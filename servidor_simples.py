#!/usr/bin/env python3
"""
Servidor HTTP simples para teste
"""

import http.server
import socketserver
import webbrowser
import threading
import time

PORT = 8080

class MyHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/':
            self.send_response(200)
            self.send_header('Content-type', 'text/html')
            self.end_headers()
            
            html = '''
            <!DOCTYPE html>
            <html>
            <head>
                <title>Teste Servidor</title>
                <meta charset="UTF-8">
            </head>
            <body>
                <h1>🎉 Servidor Funcionando!</h1>
                <p>Se você está vendo esta página, o servidor Python está rodando.</p>
                <p>Hora atual: <span id="time"></span></p>
                <script>
                    document.getElementById('time').textContent = new Date().toLocaleString();
                </script>
            </body>
            </html>
            '''
            
            self.wfile.write(html.encode())
        else:
            super().do_GET()

def abrir_navegador():
    time.sleep(2)
    webbrowser.open(f'http://localhost:{PORT}')

if __name__ == '__main__':
    print(f"🚀 Iniciando servidor HTTP na porta {PORT}...")
    print(f"📍 Acesse: http://localhost:{PORT}")
    print("-" * 40)
    
    try:
        # Abrir navegador em thread separada
        threading.Thread(target=abrir_navegador, daemon=True).start()
        
        with socketserver.TCPServer(("", PORT), MyHTTPRequestHandler) as httpd:
            print(f"✅ Servidor rodando em http://localhost:{PORT}")
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n🛑 Servidor interrompido pelo usuário")
    except Exception as e:
        print(f"❌ Erro: {e}")
        input("Pressione Enter para sair...")
