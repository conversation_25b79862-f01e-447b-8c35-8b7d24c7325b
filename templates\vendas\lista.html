{% extends "base.html" %}

{% block title %}Vendas - Saúde Flex{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-shopping-cart me-2 text-primary"></i>
                Vendas
            </h1>
            <a href="{{ url_for('venda_nova') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                Nova Venda
            </a>
        </div>
    </div>
</div>

<!-- Filtros -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="filtro-data-inicio">Data Início</label>
                            <input type="date" class="form-control" id="filtro-data-inicio">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="filtro-data-fim">Data Fim</label>
                            <input type="date" class="form-control" id="filtro-data-fim">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="filtro-vendedor">Vendedor</label>
                            <select class="form-control" id="filtro-vendedor">
                                <option value="">Todos os vendedores</option>
                                <!-- Será preenchido via JavaScript -->
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label>&nbsp;</label>
                            <button type="button" class="btn btn-outline-secondary w-100" onclick="limparFiltros()">
                                <i class="fas fa-eraser me-1"></i>
                                Limpar
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Resumo de Vendas -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Total de Vendas
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="total-vendas">
                            {{ vendas|length if vendas else 0 }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Faturamento Total
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="faturamento-total">
                            R$ 0,00
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Ticket Médio
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="ticket-medio">
                            R$ 0,00
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Vendas Hoje
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="vendas-hoje">
                            0
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-calendar-day fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Lista de Vendas -->
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-list me-2"></i>
                    Lista de Vendas
                </h6>
            </div>
            <div class="card-body">
                {% if vendas %}
                <div class="table-responsive">
                    <table class="table table-hover" id="tabela-vendas">
                        <thead>
                            <tr>
                                <th>Data</th>
                                <th>Cliente</th>
                                <th>Vendedor</th>
                                <th>Subtotal</th>
                                <th>Desconto</th>
                                <th>Total</th>
                                <th>Ações</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for venda in vendas %}
                            <tr>
                                <td>
                                    <i class="fas fa-calendar me-2 text-muted"></i>
                                    {{ venda.data_venda }}
                                </td>
                                <td>
                                    <i class="fas fa-user me-2 text-muted"></i>
                                    {{ venda.cliente_nome }}
                                </td>
                                <td>
                                    <i class="fas fa-user-tie me-2 text-muted"></i>
                                    {{ venda.vendedor_nome }}
                                </td>
                                <td>
                                    <span class="text-muted">R$</span>
                                    {{ "%.2f"|format(venda.subtotal) }}
                                </td>
                                <td>
                                    {% if venda.desconto_total > 0 %}
                                    <span class="text-warning">-R$ {{ "%.2f"|format(venda.desconto_total) }}</span>
                                    {% else %}
                                    <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <strong class="text-success">R$ {{ "%.2f"|format(venda.total_final) }}</strong>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-sm btn-outline-primary" 
                                                onclick="verVenda({{ venda.id }})" title="Ver Detalhes">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-info" 
                                                onclick="imprimirComprovante({{ venda.id }})" title="Imprimir Comprovante">
                                            <i class="fas fa-print"></i>
                                        </button>
                                        {% if usuario_logado.tipo in ['admin', 'gerente'] %}
                                        <button type="button" class="btn btn-sm btn-outline-danger" 
                                                onclick="cancelarVenda({{ venda.id }})" title="Cancelar Venda">
                                            <i class="fas fa-times"></i>
                                        </button>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">Nenhuma venda realizada</h5>
                    <p class="text-muted">Comece realizando sua primeira venda.</p>
                    <a href="{{ url_for('venda_nova') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        Realizar Primeira Venda
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Modal de Detalhes da Venda -->
<div class="modal fade" id="modalDetalhesVenda" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-shopping-cart me-2"></i>
                    Detalhes da Venda
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="conteudo-detalhes-venda">
                <!-- Conteúdo será carregado via JavaScript -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
                <button type="button" class="btn btn-info" onclick="imprimirComprovanteModal()">
                    <i class="fas fa-print me-2"></i>
                    Imprimir Comprovante
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}
.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}
.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}
.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}
.text-xs {
    font-size: 0.7rem;
}
.text-gray-800 {
    color: #5a5c69 !important;
}
.text-gray-300 {
    color: #dddfeb !important;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
let vendaAtual = null;

function limparFiltros() {
    document.getElementById('filtro-data-inicio').value = '';
    document.getElementById('filtro-data-fim').value = '';
    document.getElementById('filtro-vendedor').value = '';
}

function verVenda(id) {
    vendaAtual = id;
    
    // Simular carregamento de dados da venda
    const conteudo = `
        <div class="row">
            <div class="col-md-6">
                <h6><i class="fas fa-info-circle me-2"></i>Informações da Venda</h6>
                <p><strong>Data:</strong> 15/06/2024 14:30</p>
                <p><strong>Vendedor:</strong> João Silva</p>
                <p><strong>Cliente:</strong> Maria Santos</p>
            </div>
            <div class="col-md-6">
                <h6><i class="fas fa-calculator me-2"></i>Valores</h6>
                <p><strong>Subtotal:</strong> R$ 250,00</p>
                <p><strong>Desconto:</strong> R$ 25,00</p>
                <p><strong>Total Final:</strong> <span class="text-success">R$ 225,00</span></p>
            </div>
        </div>
        <hr>
        <div class="row">
            <div class="col-12">
                <h6><i class="fas fa-list me-2"></i>Itens da Venda</h6>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Produto</th>
                                <th>Qtd</th>
                                <th>Preço Unit.</th>
                                <th>Desconto</th>
                                <th>Total</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Produto Exemplo</td>
                                <td>2</td>
                                <td>R$ 125,00</td>
                                <td>R$ 25,00</td>
                                <td>R$ 225,00</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    `;
    
    document.getElementById('conteudo-detalhes-venda').innerHTML = conteudo;
    
    const modal = new bootstrap.Modal(document.getElementById('modalDetalhesVenda'));
    modal.show();
}

function imprimirComprovante(id) {
    // Implementar impressão de comprovante
    SaudeFlex.notify.info('Gerando comprovante...');
    // Simular download do PDF
    setTimeout(() => {
        SaudeFlex.notify.success('Comprovante gerado com sucesso!');
    }, 2000);
}

function imprimirComprovanteModal() {
    if (vendaAtual) {
        imprimirComprovante(vendaAtual);
    }
}

function cancelarVenda(id) {
    SaudeFlex.modal.confirm(
        'Tem certeza que deseja cancelar esta venda? Esta ação não pode ser desfeita.',
        function() {
            // Implementar cancelamento
            SaudeFlex.notify.success('Venda cancelada com sucesso!');
            location.reload();
        }
    );
}

// Inicialização
document.addEventListener('DOMContentLoaded', function() {
    // Definir data padrão para filtros (últimos 30 dias)
    const hoje = new Date();
    const trintaDiasAtras = new Date(hoje.getTime() - (30 * 24 * 60 * 60 * 1000));
    
    document.getElementById('filtro-data-inicio').value = trintaDiasAtras.toISOString().split('T')[0];
    document.getElementById('filtro-data-fim').value = hoje.toISOString().split('T')[0];
});
</script>
{% endblock %}
