#!/usr/bin/env python3
"""
Versão simplificada do Saúde Flex para teste
"""

from flask import Flask, render_template, request, redirect, url_for, session, flash
import os
import sqlite3
import bcrypt
from datetime import datetime

# Criar app Flask simples
app = Flask(__name__)
app.secret_key = 'saude_flex_secret_key_2024'

# Configurações básicas
DATABASE = 'saude_flex_simples.db'

def init_db():
    """Inicializar banco de dados simples"""
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    # Criar tabela de usuários
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS usuarios (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            nome TEXT NOT NULL,
            email TEXT UNIQUE NOT NULL,
            senha TEXT NOT NULL,
            tipo TEXT NOT NULL DEFAULT 'admin'
        )
    ''')
    
    # Verificar se já existe um admin
    cursor.execute("SELECT id FROM usuarios WHERE tipo = 'admin' LIMIT 1")
    if not cursor.fetchone():
        # Criar usuário admin padrão
        senha_hash = bcrypt.hashpw('admin123'.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
        cursor.execute('''
            INSERT INTO usuarios (nome, email, senha, tipo)
            VALUES (?, ?, ?, ?)
        ''', ('Administrador', '<EMAIL>', senha_hash, 'admin'))
        print("Usuário admin criado: <EMAIL> / admin123")
    
    conn.commit()
    conn.close()

def get_db():
    """Obter conexão com banco"""
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    return conn

@app.route('/')
def index():
    """Página inicial"""
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    return render_template_string('''
    <!DOCTYPE html>
    <html lang="pt-BR">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Saúde Flex - Dashboard</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    </head>
    <body>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="/">
                    <i class="fas fa-heartbeat me-2"></i>Saúde Flex
                </a>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="{{ url_for('logout') }}">
                        <i class="fas fa-sign-out-alt me-1"></i>Sair
                    </a>
                </div>
            </div>
        </nav>
        
        <div class="container mt-4">
            <div class="row">
                <div class="col-12">
                    <h1 class="h3 mb-4">
                        <i class="fas fa-tachometer-alt me-2 text-primary"></i>
                        Dashboard - Saúde Flex
                    </h1>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="card border-primary">
                        <div class="card-body text-center">
                            <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                            <h5 class="card-title">Sistema Funcionando!</h5>
                            <p class="card-text">
                                O Saúde Flex está rodando corretamente.<br>
                                Usuário logado: <strong>{{ session.user_nome }}</strong>
                            </p>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card border-info">
                        <div class="card-body">
                            <h5 class="card-title">
                                <i class="fas fa-info-circle me-2"></i>Informações
                            </h5>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i>Flask funcionando</li>
                                <li><i class="fas fa-check text-success me-2"></i>Banco de dados conectado</li>
                                <li><i class="fas fa-check text-success me-2"></i>Autenticação ativa</li>
                                <li><i class="fas fa-check text-success me-2"></i>Templates carregando</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row mt-4">
                <div class="col-12">
                    <div class="alert alert-success">
                        <h6><i class="fas fa-rocket me-2"></i>Próximos Passos:</h6>
                        <ol>
                            <li>O sistema básico está funcionando</li>
                            <li>Agora podemos ativar o sistema completo</li>
                            <li>Todas as funcionalidades estão implementadas</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
        
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    </body>
    </html>
    ''')

@app.route('/login', methods=['GET', 'POST'])
def login():
    """Página de login"""
    if request.method == 'POST':
        email = request.form['email']
        senha = request.form['senha']
        
        conn = get_db()
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM usuarios WHERE email = ?', (email,))
        usuario = cursor.fetchone()
        conn.close()
        
        if usuario and bcrypt.checkpw(senha.encode('utf-8'), usuario['senha'].encode('utf-8')):
            session['user_id'] = usuario['id']
            session['user_nome'] = usuario['nome']
            session['user_tipo'] = usuario['tipo']
            
            flash('Login realizado com sucesso!', 'success')
            return redirect(url_for('index'))
        else:
            flash('Email ou senha incorretos!', 'error')
    
    return render_template_string('''
    <!DOCTYPE html>
    <html lang="pt-BR">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Login - Saúde Flex</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    </head>
    <body class="bg-light">
        <div class="container">
            <div class="row justify-content-center mt-5">
                <div class="col-md-6">
                    <div class="card shadow">
                        <div class="card-body">
                            <div class="text-center mb-4">
                                <i class="fas fa-heartbeat fa-3x text-primary"></i>
                                <h3 class="mt-2">Saúde Flex</h3>
                                <p class="text-muted">Sistema de Agendamentos</p>
                            </div>
                            
                            {% with messages = get_flashed_messages(with_categories=true) %}
                                {% if messages %}
                                    {% for category, message in messages %}
                                        <div class="alert alert-{{ 'danger' if category == 'error' else 'success' }} alert-dismissible fade show">
                                            {{ message }}
                                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                        </div>
                                    {% endfor %}
                                {% endif %}
                            {% endwith %}
                            
                            <form method="POST">
                                <div class="mb-3">
                                    <label for="email" class="form-label">Email</label>
                                    <input type="email" class="form-control" id="email" name="email" required>
                                </div>
                                <div class="mb-3">
                                    <label for="senha" class="form-label">Senha</label>
                                    <input type="password" class="form-control" id="senha" name="senha" required>
                                </div>
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-sign-in-alt me-2"></i>Entrar
                                </button>
                            </form>
                            
                            <div class="mt-4 text-center">
                                <small class="text-muted">
                                    <strong>Login padrão:</strong><br>
                                    Email: <EMAIL><br>
                                    Senha: admin123
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    </body>
    </html>
    ''')

@app.route('/logout')
def logout():
    """Logout"""
    session.clear()
    flash('Logout realizado com sucesso!', 'success')
    return redirect(url_for('login'))

@app.route('/test')
def test():
    """Página de teste"""
    return '''
    <h1>🎉 Sistema Funcionando!</h1>
    <p>Se você está vendo esta página, o Flask está rodando corretamente.</p>
    <p><a href="/">Ir para o Dashboard</a></p>
    '''

if __name__ == '__main__':
    print("🚀 Iniciando Saúde Flex...")
    print("📁 Inicializando banco de dados...")
    
    try:
        init_db()
        print("✅ Banco de dados inicializado")
        print("🌐 Iniciando servidor Flask...")
        print("📍 Acesse: http://localhost:5000")
        print("🔑 Login: <EMAIL> / admin123")
        print("-" * 50)
        
        app.run(debug=True, host='127.0.0.1', port=5000)
        
    except Exception as e:
        print(f"❌ Erro ao iniciar: {e}")
        import traceback
        traceback.print_exc()
