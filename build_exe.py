#!/usr/bin/env python3
"""
Script para compilar o Saúde Flex em executável (.exe)
Uso: python build_exe.py
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def check_dependencies():
    """Verifica se todas as dependências estão instaladas"""
    try:
        import PyInstaller
        print("✓ PyInstaller encontrado")
    except ImportError:
        print("❌ PyInstaller não encontrado. Instalando...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("✓ PyInstaller instalado")

def create_spec_file():
    """Cria arquivo .spec personalizado para o PyInstaller"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['app.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('templates', 'templates'),
        ('static', 'static'),
        ('config.py', '.'),
        ('database.py', '.'),
        ('models.py', '.'),
        ('backup_system.py', '.'),
    ],
    hiddenimports=[
        'sqlite3',
        'bcrypt',
        'flask',
        'jinja2',
        'werkzeug',
        'reportlab',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='SaudeFlex',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # False para não mostrar console
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='static/img/icon.ico' if os.path.exists('static/img/icon.ico') else None,
)
'''
    
    with open('saude_flex.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✓ Arquivo .spec criado")

def build_executable():
    """Compila o executável"""
    print("🔨 Iniciando compilação...")
    
    try:
        # Executar PyInstaller
        cmd = [
            sys.executable, '-m', 'PyInstaller',
            '--clean',
            '--noconfirm',
            'saude_flex.spec'
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ Compilação concluída com sucesso!")
            
            # Verificar se o executável foi criado
            exe_path = Path('dist/SaudeFlex.exe')
            if exe_path.exists():
                size_mb = exe_path.stat().st_size / (1024 * 1024)
                print(f"✓ Executável criado: {exe_path} ({size_mb:.1f} MB)")
                
                # Criar pasta de distribuição
                create_distribution()
            else:
                print("❌ Executável não encontrado após compilação")
        else:
            print("❌ Erro na compilação:")
            print(result.stderr)
            
    except Exception as e:
        print(f"❌ Erro durante compilação: {e}")

def create_distribution():
    """Cria pasta de distribuição com arquivos necessários"""
    dist_dir = Path('SaudeFlex_Distribuicao')
    
    # Remover pasta anterior se existir
    if dist_dir.exists():
        shutil.rmtree(dist_dir)
    
    # Criar nova pasta
    dist_dir.mkdir()
    
    # Copiar executável
    shutil.copy2('dist/SaudeFlex.exe', dist_dir)
    
    # Criar pastas necessárias
    (dist_dir / 'backups').mkdir()
    (dist_dir / 'static' / 'img' / 'produtos').mkdir(parents=True)
    
    # Copiar arquivos de exemplo/configuração
    readme_content = """# Saúde Flex - Sistema de Agendamentos

## Como usar:
1. Execute o arquivo SaudeFlex.exe
2. O sistema abrirá automaticamente no navegador
3. Use as credenciais padrão:
   - Email: <EMAIL>
   - Senha: admin123

## Primeiro uso:
- Altere a senha do administrador
- Cadastre usuários, produtos e categorias
- Configure o sistema conforme sua necessidade

## Backup:
- O sistema possui backup automático
- Acesse Admin > Backup para criar backups manuais

## Suporte:
- Mantenha este arquivo junto com o executável
- Em caso de problemas, verifique se todas as pastas estão presentes
"""
    
    with open(dist_dir / 'LEIA-ME.txt', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print(f"✓ Distribuição criada em: {dist_dir}")
    print("✓ Pronto para distribuição!")

def main():
    """Função principal"""
    print("🚀 Compilador Saúde Flex para EXE")
    print("=" * 40)
    
    # Verificar se estamos no diretório correto
    if not Path('app.py').exists():
        print("❌ Arquivo app.py não encontrado. Execute este script na pasta do projeto.")
        return
    
    # Verificar dependências
    check_dependencies()
    
    # Criar arquivo .spec
    create_spec_file()
    
    # Compilar
    build_executable()
    
    print("\n🎉 Processo concluído!")
    print("📁 Verifique a pasta 'SaudeFlex_Distribuicao' para os arquivos finais.")

if __name__ == '__main__':
    main()
