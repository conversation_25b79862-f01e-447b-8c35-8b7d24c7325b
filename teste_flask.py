#!/usr/bin/env python3
"""
Teste básico do Flask
"""

from flask import Flask

app = Flask(__name__)

@app.route('/')
def home():
    return '''
    <h1>🎉 Flask Funcionando!</h1>
    <p>Se você está vendo esta página, o Flask está rodando corretamente.</p>
    <p>Hora: <span id="time"></span></p>
    <script>
        document.getElementById('time').textContent = new Date().toLocaleString();
    </script>
    '''

@app.route('/test')
def test():
    return 'Teste OK!'

if __name__ == '__main__':
    print("🚀 Iniciando teste Flask...")
    print("📍 Acesse: http://localhost:8080")
    print("📍 Ou teste: http://localhost:8080/test")
    print("-" * 40)

    try:
        app.run(debug=True, host='127.0.0.1', port=8080)
    except Exception as e:
        print(f"Erro: {e}")
        input("Pressione Enter para sair...")
