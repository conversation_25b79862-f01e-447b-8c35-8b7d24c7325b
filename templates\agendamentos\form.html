{% extends "base.html" %}

{% block title %}{{ 'Editar' if agendamento else 'Novo' }} Agendamento - Saúde Flex{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-{{ 'edit' if agendamento else 'calendar-plus' }} me-2 text-primary"></i>
                {{ 'Editar' if agendamento else 'Novo' }} Agendamento
            </h1>
            <a href="{{ url_for('agendamentos') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>
                Voltar
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-info-circle me-2"></i>
                    Informações do Agendamento
                </h6>
            </div>
            <div class="card-body">
                <form method="POST" id="form-agendamento">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="cliente_id" class="form-label">
                                    <i class="fas fa-user me-1"></i>
                                    Cliente *
                                </label>
                                <select class="form-control" id="cliente_id" name="cliente_id" required>
                                    <option value="">Selecione um cliente</option>
                                    {% for cliente in clientes %}
                                    <option value="{{ cliente.id }}" 
                                            data-telefone="{{ cliente.telefone }}"
                                            data-endereco="{{ cliente.logradouro }}{% if cliente.numero %}, {{ cliente.numero }}{% endif %}"
                                            {{ 'selected' if agendamento and agendamento.cliente_id == cliente.id else '' }}>
                                        {{ cliente.nome }}
                                    </option>
                                    {% endfor %}
                                </select>
                                <small class="text-muted">
                                    <a href="{{ url_for('cliente_novo') }}" target="_blank">
                                        <i class="fas fa-plus me-1"></i>
                                        Cadastrar novo cliente
                                    </a>
                                </small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="produto_id" class="form-label">
                                    <i class="fas fa-box me-1"></i>
                                    Produto de Interesse *
                                </label>
                                <select class="form-control" id="produto_id" name="produto_id" required>
                                    <option value="">Selecione um produto</option>
                                    {% for produto in produtos %}
                                    <option value="{{ produto.id }}" 
                                            data-preco="{{ produto.preco_venda }}"
                                            {{ 'selected' if agendamento and agendamento.produto_id == produto.id else '' }}>
                                        {{ produto.nome }} - R$ {{ "%.2f"|format(produto.preco_venda) }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="vendedor_id" class="form-label">
                                    <i class="fas fa-user-tie me-1"></i>
                                    Vendedor Responsável *
                                </label>
                                <select class="form-control" id="vendedor_id" name="vendedor_id" required>
                                    <option value="">Selecione um vendedor</option>
                                    {% for vendedor in vendedores %}
                                    <option value="{{ vendedor.id }}" 
                                            {{ 'selected' if agendamento and agendamento.vendedor_id == vendedor.id else '' }}>
                                        {{ vendedor.nome }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="data_agendamento" class="form-label">
                                    <i class="fas fa-calendar-alt me-1"></i>
                                    Data e Hora *
                                </label>
                                <input type="datetime-local" class="form-control" id="data_agendamento" name="data_agendamento" 
                                       value="{{ agendamento.data_agendamento if agendamento else '' }}" required>
                            </div>
                        </div>
                    </div>

                    <div class="form-group mb-4">
                        <label for="observacoes" class="form-label">
                            <i class="fas fa-sticky-note me-1"></i>
                            Observações
                        </label>
                        <textarea class="form-control" id="observacoes" name="observacoes" rows="4" 
                                  placeholder="Observações sobre o agendamento, preferências do cliente, etc...">{{ agendamento.observacoes if agendamento else '' }}</textarea>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('agendamentos') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>
                            Cancelar
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            {{ 'Atualizar' if agendamento else 'Agendar' }} Visita
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- Preview do Agendamento -->
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-eye me-2"></i>
                    Preview do Agendamento
                </h6>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    <div class="bg-primary rounded d-flex align-items-center justify-content-center mx-auto" 
                         style="width: 80px; height: 80px;">
                        <i class="fas fa-calendar-alt fa-2x text-white"></i>
                    </div>
                </div>
                
                <div class="mt-3">
                    <h6 class="text-primary">
                        <i class="fas fa-user me-2"></i>
                        Cliente
                    </h6>
                    <p id="preview-cliente" class="mb-2">Selecione um cliente</p>
                    <p id="preview-cliente-info" class="text-muted small mb-3"></p>
                    
                    <h6 class="text-primary">
                        <i class="fas fa-box me-2"></i>
                        Produto
                    </h6>
                    <p id="preview-produto" class="mb-3">Selecione um produto</p>
                    
                    <h6 class="text-primary">
                        <i class="fas fa-user-tie me-2"></i>
                        Vendedor
                    </h6>
                    <p id="preview-vendedor" class="mb-3">Selecione um vendedor</p>
                    
                    <h6 class="text-primary">
                        <i class="fas fa-calendar-alt me-2"></i>
                        Data e Hora
                    </h6>
                    <p id="preview-data" class="mb-3">Selecione data e hora</p>
                    
                    <h6 class="text-primary">
                        <i class="fas fa-sticky-note me-2"></i>
                        Observações
                    </h6>
                    <p id="preview-observacoes" class="text-muted">Nenhuma observação</p>
                </div>
            </div>
        </div>

        <!-- Verificação de Conflitos -->
        <div class="card shadow mt-4" id="card-conflitos" style="display: none;">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Conflitos de Horário
                </h6>
            </div>
            <div class="card-body">
                <div id="lista-conflitos">
                    <!-- Conflitos serão listados aqui -->
                </div>
            </div>
        </div>

        <!-- Dicas -->
        <div class="card shadow mt-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-info">
                    <i class="fas fa-lightbulb me-2"></i>
                    Dicas
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        <small>Verifique a disponibilidade do vendedor</small>
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        <small>Confirme o endereço com o cliente</small>
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        <small>Agende com pelo menos 24h de antecedência</small>
                    </li>
                    <li>
                        <i class="fas fa-check text-success me-2"></i>
                        <small>Adicione observações importantes</small>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Atualizar preview em tempo real
document.getElementById('cliente_id').addEventListener('change', function() {
    const option = this.options[this.selectedIndex];
    if (option.value) {
        document.getElementById('preview-cliente').textContent = option.text;
        const telefone = option.dataset.telefone;
        const endereco = option.dataset.endereco;
        document.getElementById('preview-cliente-info').innerHTML = 
            `<i class="fas fa-phone me-1"></i>${telefone}<br><i class="fas fa-map-marker-alt me-1"></i>${endereco}`;
    } else {
        document.getElementById('preview-cliente').textContent = 'Selecione um cliente';
        document.getElementById('preview-cliente-info').textContent = '';
    }
});

document.getElementById('produto_id').addEventListener('change', function() {
    const option = this.options[this.selectedIndex];
    if (option.value) {
        document.getElementById('preview-produto').textContent = option.text;
    } else {
        document.getElementById('preview-produto').textContent = 'Selecione um produto';
    }
});

document.getElementById('vendedor_id').addEventListener('change', function() {
    const option = this.options[this.selectedIndex];
    if (option.value) {
        document.getElementById('preview-vendedor').textContent = option.text;
        verificarConflitos();
    } else {
        document.getElementById('preview-vendedor').textContent = 'Selecione um vendedor';
    }
});

document.getElementById('data_agendamento').addEventListener('change', function() {
    if (this.value) {
        const data = new Date(this.value);
        document.getElementById('preview-data').textContent = data.toLocaleString('pt-BR');
        verificarConflitos();
    } else {
        document.getElementById('preview-data').textContent = 'Selecione data e hora';
    }
});

document.getElementById('observacoes').addEventListener('input', function() {
    document.getElementById('preview-observacoes').textContent = this.value || 'Nenhuma observação';
});

// Verificar conflitos de horário
function verificarConflitos() {
    const vendedorId = document.getElementById('vendedor_id').value;
    const dataAgendamento = document.getElementById('data_agendamento').value;
    
    if (vendedorId && dataAgendamento) {
        // Simular verificação de conflitos
        // Em uma implementação real, faria uma requisição AJAX para o servidor
        const cardConflitos = document.getElementById('card-conflitos');
        const listaConflitos = document.getElementById('lista-conflitos');
        
        // Exemplo de conflito (remover em implementação real)
        const temConflito = Math.random() > 0.7; // 30% de chance de conflito
        
        if (temConflito) {
            cardConflitos.style.display = 'block';
            listaConflitos.innerHTML = `
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Conflito detectado!</strong><br>
                    O vendedor já possui um agendamento próximo a este horário.
                </div>
            `;
        } else {
            cardConflitos.style.display = 'none';
        }
    }
}

// Validação do formulário
document.getElementById('form-agendamento').addEventListener('submit', function(e) {
    const dataAgendamento = new Date(document.getElementById('data_agendamento').value);
    const agora = new Date();
    
    if (dataAgendamento <= agora) {
        e.preventDefault();
        SaudeFlex.notify.error('A data do agendamento deve ser futura.');
        return;
    }
    
    // Verificar se é horário comercial (8h às 18h)
    const hora = dataAgendamento.getHours();
    if (hora < 8 || hora >= 18) {
        e.preventDefault();
        SaudeFlex.notify.warning('Recomendamos agendar em horário comercial (8h às 18h).');
        if (!confirm('Deseja continuar mesmo assim?')) {
            return;
        }
    }
});

// Definir data mínima como hoje
document.addEventListener('DOMContentLoaded', function() {
    const agora = new Date();
    agora.setMinutes(agora.getMinutes() - agora.getTimezoneOffset());
    document.getElementById('data_agendamento').min = agora.toISOString().slice(0, 16);
});
</script>
{% endblock %}
