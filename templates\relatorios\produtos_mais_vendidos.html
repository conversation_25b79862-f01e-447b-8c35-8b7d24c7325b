{% extends "base.html" %}

{% block title %}Produtos Mais Vendidos - Saúde Flex{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-trophy me-2 text-warning"></i>
                Produtos Mais Vendidos
            </h1>
            <div class="btn-group" role="group">
                <a href="{{ url_for('relatorios') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>
                    Voltar
                </a>
                <button type="button" class="btn btn-outline-primary" onclick="exportarRelatorio()">
                    <i class="fas fa-download me-2"></i>
                    Exportar PDF
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Filtros -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="periodo-filtro">Período</label>
                            <select class="form-control" id="periodo-filtro">
                                <option value="30">Últimos 30 dias</option>
                                <option value="90">Últimos 90 dias</option>
                                <option value="180">Últimos 6 meses</option>
                                <option value="365">Último ano</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="categoria-filtro">Categoria</label>
                            <select class="form-control" id="categoria-filtro">
                                <option value="">Todas as categorias</option>
                                <option value="bem-estar">Bem-estar</option>
                                <option value="saude">Saúde</option>
                                <option value="beleza">Beleza</option>
                                <option value="suplementos">Suplementos</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="limite-produtos">Mostrar Top</label>
                            <select class="form-control" id="limite-produtos">
                                <option value="10">10 produtos</option>
                                <option value="20">20 produtos</option>
                                <option value="50">50 produtos</option>
                                <option value="100">100 produtos</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label>&nbsp;</label>
                            <button type="button" class="btn btn-primary w-100" onclick="aplicarFiltros()">
                                <i class="fas fa-search me-2"></i>
                                Aplicar Filtros
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Resumo -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Produto Campeão
                        </div>
                        <div class="h6 mb-0 font-weight-bold text-gray-800" id="produto-campeao">
                            Vitamina C 1000mg
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-crown fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Total Vendido
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="total-vendido">
                            1.247 unidades
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-boxes fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Faturamento Top 10
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="faturamento-top10">
                            R$ 28.450,00
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Produtos Analisados
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="produtos-analisados">
                            45
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-chart-bar fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Gráficos -->
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-chart-bar me-2"></i>
                    Top 10 Produtos Mais Vendidos
                </h6>
            </div>
            <div class="card-body">
                <canvas id="graficoTop10" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
    <div class="col-lg-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-chart-pie me-2"></i>
                    Vendas por Categoria
                </h6>
            </div>
            <div class="card-body">
                <canvas id="graficoCategorias" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Ranking Detalhado -->
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-list-ol me-2"></i>
                    Ranking Detalhado
                </h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Posição</th>
                                <th>Produto</th>
                                <th>Categoria</th>
                                <th>Qtd Vendida</th>
                                <th>Faturamento</th>
                                <th>Lucro</th>
                                <th>% do Total</th>
                                <th>Tendência</th>
                            </tr>
                        </thead>
                        <tbody id="ranking-produtos">
                            <!-- Dados serão carregados via JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}
.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}
.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}
.text-xs {
    font-size: 0.7rem;
}
.text-gray-800 {
    color: #5a5c69 !important;
}
.text-gray-300 {
    color: #dddfeb !important;
}
.posicao-badge {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    color: white;
}
.posicao-1 { background-color: #FFD700; color: #000; }
.posicao-2 { background-color: #C0C0C0; color: #000; }
.posicao-3 { background-color: #CD7F32; color: #fff; }
.posicao-outros { background-color: #6c757d; }
</style>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Dados simulados
const produtosMaisVendidos = [
    {posicao: 1, nome: 'Vitamina C 1000mg', categoria: 'Suplementos', quantidade: 245, faturamento: 4900.00, lucro: 1470.00, percentual: 18.5, tendencia: 'up'},
    {posicao: 2, nome: 'Colágeno Hidrolisado', categoria: 'Beleza', quantidade: 198, faturamento: 4752.00, lucro: 1425.60, percentual: 14.9, tendencia: 'up'},
    {posicao: 3, nome: 'Whey Protein', categoria: 'Suplementos', quantidade: 156, faturamento: 4680.00, lucro: 1404.00, percentual: 11.8, tendencia: 'down'},
    {posicao: 4, nome: 'Ômega 3', categoria: 'Saúde', quantidade: 134, faturamento: 3350.00, lucro: 1005.00, percentual: 10.1, tendencia: 'up'},
    {posicao: 5, nome: 'Multivitamínico', categoria: 'Saúde', quantidade: 112, faturamento: 2800.00, lucro: 840.00, percentual: 8.4, tendencia: 'stable'},
    {posicao: 6, nome: 'Probiótico', categoria: 'Saúde', quantidade: 98, faturamento: 2450.00, lucro: 735.00, percentual: 7.4, tendencia: 'up'},
    {posicao: 7, nome: 'Magnésio', categoria: 'Suplementos', quantidade: 87, faturamento: 1740.00, lucro: 522.00, percentual: 6.6, tendencia: 'down'},
    {posicao: 8, nome: 'Vitamina D3', categoria: 'Saúde', quantidade: 76, faturamento: 1520.00, lucro: 456.00, percentual: 5.7, tendencia: 'up'},
    {posicao: 9, nome: 'Creatina', categoria: 'Suplementos', quantidade: 65, faturamento: 1300.00, lucro: 390.00, percentual: 4.9, tendencia: 'stable'},
    {posicao: 10, nome: 'Coenzima Q10', categoria: 'Bem-estar', quantidade: 54, faturamento: 1620.00, lucro: 486.00, percentual: 4.1, tendencia: 'up'}
];

// Gráfico Top 10
const ctxTop10 = document.getElementById('graficoTop10').getContext('2d');
const graficoTop10 = new Chart(ctxTop10, {
    type: 'bar',
    data: {
        labels: produtosMaisVendidos.slice(0, 10).map(p => p.nome),
        datasets: [{
            label: 'Quantidade Vendida',
            data: produtosMaisVendidos.slice(0, 10).map(p => p.quantidade),
            backgroundColor: [
                '#FFD700', '#C0C0C0', '#CD7F32', '#4e73df', '#1cc88a',
                '#36b9cc', '#f6c23e', '#e74a3b', '#6f42c1', '#fd7e14'
            ]
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// Gráfico por Categoria
const ctxCategorias = document.getElementById('graficoCategorias').getContext('2d');
const categorias = {};
produtosMaisVendidos.forEach(produto => {
    if (!categorias[produto.categoria]) {
        categorias[produto.categoria] = 0;
    }
    categorias[produto.categoria] += produto.quantidade;
});

const graficoCategorias = new Chart(ctxCategorias, {
    type: 'doughnut',
    data: {
        labels: Object.keys(categorias),
        datasets: [{
            data: Object.values(categorias),
            backgroundColor: ['#4e73df', '#1cc88a', '#36b9cc', '#f6c23e']
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

// Carregar ranking na tabela
function carregarRanking() {
    const tbody = document.getElementById('ranking-produtos');
    tbody.innerHTML = '';
    
    produtosMaisVendidos.forEach(produto => {
        const tr = document.createElement('tr');
        
        let posicaoClass = 'posicao-outros';
        if (produto.posicao === 1) posicaoClass = 'posicao-1';
        else if (produto.posicao === 2) posicaoClass = 'posicao-2';
        else if (produto.posicao === 3) posicaoClass = 'posicao-3';
        
        let tendenciaIcon = '';
        let tendenciaColor = '';
        if (produto.tendencia === 'up') {
            tendenciaIcon = 'fa-arrow-up';
            tendenciaColor = 'text-success';
        } else if (produto.tendencia === 'down') {
            tendenciaIcon = 'fa-arrow-down';
            tendenciaColor = 'text-danger';
        } else {
            tendenciaIcon = 'fa-minus';
            tendenciaColor = 'text-warning';
        }
        
        tr.innerHTML = `
            <td>
                <div class="posicao-badge ${posicaoClass}">
                    ${produto.posicao}
                </div>
            </td>
            <td>
                <strong>${produto.nome}</strong>
            </td>
            <td>
                <span class="badge bg-secondary">${produto.categoria}</span>
            </td>
            <td>
                <strong>${produto.quantidade}</strong> unidades
            </td>
            <td>
                <strong class="text-success">R$ ${produto.faturamento.toFixed(2)}</strong>
            </td>
            <td>
                <strong class="text-warning">R$ ${produto.lucro.toFixed(2)}</strong>
            </td>
            <td>
                ${produto.percentual}%
            </td>
            <td>
                <i class="fas ${tendenciaIcon} ${tendenciaColor}"></i>
            </td>
        `;
        
        tbody.appendChild(tr);
    });
}

function aplicarFiltros() {
    SaudeFlex.notify.success('Filtros aplicados com sucesso!');
    carregarRanking();
}

function exportarRelatorio() {
    SaudeFlex.notify.info('Gerando relatório PDF...');
    setTimeout(() => {
        SaudeFlex.notify.success('Relatório PDF gerado com sucesso!');
    }, 2000);
}

// Inicialização
document.addEventListener('DOMContentLoaded', function() {
    carregarRanking();
});
</script>
{% endblock %}
