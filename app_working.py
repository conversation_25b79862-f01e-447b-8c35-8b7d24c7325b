#!/usr/bin/env python3
"""
Saúde Flex - Sistema Completo de Agendamentos e Vendas
Versão Funcional sem dependências corrompidas
"""

from flask import Flask, render_template, request, redirect, url_for, session, flash, jsonify
from datetime import datetime
import os
import sqlite3
import hashlib  # Usando hashlib em vez de bcrypt
import json
import webbrowser
import threading
import time

# Configurações
DATABASE = 'saude_flex.db'
SECRET_KEY = 'saude_flex_secret_key_2024'
PORT = 5000

# Criar app Flask
app = Flask(__name__)
app.secret_key = SECRET_KEY

def hash_password(password):
    """Hash simples da senha usando SHA256"""
    return hashlib.sha256(password.encode()).hexdigest()

def verify_password(password, hashed):
    """Verificar senha"""
    return hashlib.sha256(password.encode()).hexdigest() == hashed

def init_database():
    """Inicializar banco de dados"""
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    # Tabela de usuários
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS usuarios (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            nome TEXT NOT NULL,
            email TEXT UNIQUE NOT NULL,
            senha TEXT NOT NULL,
            tipo TEXT NOT NULL DEFAULT 'vendedor',
            ativo INTEGER DEFAULT 1,
            data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # Tabela de categorias
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS categorias (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            nome TEXT NOT NULL,
            descricao TEXT,
            ativo INTEGER DEFAULT 1
        )
    ''')
    
    # Tabela de produtos
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS produtos (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            nome TEXT NOT NULL,
            descricao TEXT,
            categoria_id INTEGER,
            preco_custo REAL NOT NULL,
            tipo_lucro TEXT NOT NULL DEFAULT 'percentual',
            valor_lucro REAL NOT NULL DEFAULT 0,
            preco_venda REAL NOT NULL,
            estoque_atual INTEGER DEFAULT 0,
            estoque_minimo INTEGER DEFAULT 5,
            ativo INTEGER DEFAULT 1,
            data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (categoria_id) REFERENCES categorias (id)
        )
    ''')
    
    # Tabela de clientes
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS clientes (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            nome TEXT NOT NULL,
            email TEXT,
            telefone TEXT,
            cpf TEXT,
            endereco TEXT,
            cidade TEXT,
            estado TEXT,
            cep TEXT,
            data_nascimento DATE,
            observacoes TEXT,
            ativo INTEGER DEFAULT 1,
            data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # Tabela de agendamentos
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS agendamentos (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            cliente_id INTEGER NOT NULL,
            produto_id INTEGER NOT NULL,
            vendedor_id INTEGER NOT NULL,
            data_agendamento DATETIME NOT NULL,
            status TEXT DEFAULT 'agendado',
            observacoes TEXT,
            data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (cliente_id) REFERENCES clientes (id),
            FOREIGN KEY (produto_id) REFERENCES produtos (id),
            FOREIGN KEY (vendedor_id) REFERENCES usuarios (id)
        )
    ''')
    
    # Tabela de vendas
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS vendas (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            cliente_id INTEGER NOT NULL,
            vendedor_id INTEGER NOT NULL,
            subtotal REAL NOT NULL,
            desconto_total REAL DEFAULT 0,
            total_final REAL NOT NULL,
            observacoes TEXT,
            data_venda TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (cliente_id) REFERENCES clientes (id),
            FOREIGN KEY (vendedor_id) REFERENCES usuarios (id)
        )
    ''')
    
    # Tabela de itens da venda
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS itens_venda (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            venda_id INTEGER NOT NULL,
            produto_id INTEGER NOT NULL,
            quantidade INTEGER NOT NULL,
            preco_unitario REAL NOT NULL,
            desconto_item REAL DEFAULT 0,
            subtotal_item REAL NOT NULL,
            FOREIGN KEY (venda_id) REFERENCES vendas (id),
            FOREIGN KEY (produto_id) REFERENCES produtos (id)
        )
    ''')
    
    # Verificar se já existe um admin
    cursor.execute("SELECT id FROM usuarios WHERE tipo = 'admin' LIMIT 1")
    if not cursor.fetchone():
        # Criar usuário admin padrão
        senha_hash = hash_password('admin123')
        cursor.execute('''
            INSERT INTO usuarios (nome, email, senha, tipo)
            VALUES (?, ?, ?, ?)
        ''', ('Administrador', '<EMAIL>', senha_hash, 'admin'))
        print("✅ Usuário admin criado: <EMAIL> / admin123")
    
    # Criar dados de exemplo
    cursor.execute("SELECT COUNT(*) as total FROM categorias")
    if cursor.fetchone()['total'] == 0:
        cursor.execute("INSERT INTO categorias (nome, descricao) VALUES (?, ?)", 
                      ('Suplementos', 'Suplementos alimentares'))
        cursor.execute("INSERT INTO categorias (nome, descricao) VALUES (?, ?)", 
                      ('Cosméticos', 'Produtos de beleza'))
        cursor.execute("INSERT INTO categorias (nome, descricao) VALUES (?, ?)", 
                      ('Equipamentos', 'Equipamentos de exercício'))
        print("✅ Categorias de exemplo criadas")
    
    cursor.execute("SELECT COUNT(*) as total FROM produtos")
    if cursor.fetchone()['total'] == 0:
        cursor.execute('''
            INSERT INTO produtos (nome, descricao, categoria_id, preco_custo, preco_venda, estoque_atual)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', ('Whey Protein 1kg', 'Proteína em pó sabor chocolate', 1, 50.00, 89.90, 20))
        cursor.execute('''
            INSERT INTO produtos (nome, descricao, categoria_id, preco_custo, preco_venda, estoque_atual)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', ('Creatina 300g', 'Creatina monohidratada pura', 1, 30.00, 59.90, 15))
        cursor.execute('''
            INSERT INTO produtos (nome, descricao, categoria_id, preco_custo, preco_venda, estoque_atual)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', ('Shampoo Hidratante', 'Shampoo para cabelos secos', 2, 15.00, 29.90, 25))
        cursor.execute('''
            INSERT INTO produtos (nome, descricao, categoria_id, preco_custo, preco_venda, estoque_atual)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', ('Halteres 5kg', 'Par de halteres emborrachados', 3, 80.00, 149.90, 8))
        print("✅ Produtos de exemplo criados")
    
    cursor.execute("SELECT COUNT(*) as total FROM clientes")
    if cursor.fetchone()['total'] == 0:
        cursor.execute('''
            INSERT INTO clientes (nome, email, telefone, cidade, endereco)
            VALUES (?, ?, ?, ?, ?)
        ''', ('João Silva', '<EMAIL>', '(11) 99999-9999', 'São Paulo', 'Rua das Flores, 123'))
        cursor.execute('''
            INSERT INTO clientes (nome, email, telefone, cidade, endereco)
            VALUES (?, ?, ?, ?, ?)
        ''', ('Maria Santos', '<EMAIL>', '(11) 88888-8888', 'Rio de Janeiro', 'Av. Copacabana, 456'))
        cursor.execute('''
            INSERT INTO clientes (nome, email, telefone, cidade, endereco)
            VALUES (?, ?, ?, ?, ?)
        ''', ('Pedro Costa', '<EMAIL>', '(11) 77777-7777', 'Belo Horizonte', 'Rua da Liberdade, 789'))
        print("✅ Clientes de exemplo criados")
    
    # Criar vendas de exemplo
    cursor.execute("SELECT COUNT(*) as total FROM vendas")
    if cursor.fetchone()['total'] == 0:
        # Venda 1
        cursor.execute('''
            INSERT INTO vendas (cliente_id, vendedor_id, subtotal, desconto_total, total_final, observacoes)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (1, 1, 149.80, 10.00, 139.80, 'Primeira compra - desconto especial'))
        
        cursor.execute('''
            INSERT INTO itens_venda (venda_id, produto_id, quantidade, preco_unitario, desconto_item, subtotal_item)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (1, 1, 1, 89.90, 0, 89.90))
        
        cursor.execute('''
            INSERT INTO itens_venda (venda_id, produto_id, quantidade, preco_unitario, desconto_item, subtotal_item)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (1, 2, 1, 59.90, 0, 59.90))
        
        # Venda 2
        cursor.execute('''
            INSERT INTO vendas (cliente_id, vendedor_id, subtotal, desconto_total, total_final)
            VALUES (?, ?, ?, ?, ?)
        ''', (2, 1, 179.70, 0, 179.70))
        
        cursor.execute('''
            INSERT INTO itens_venda (venda_id, produto_id, quantidade, preco_unitario, desconto_item, subtotal_item)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (2, 4, 1, 149.90, 0, 149.90))
        
        cursor.execute('''
            INSERT INTO itens_venda (venda_id, produto_id, quantidade, preco_unitario, desconto_item, subtotal_item)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (2, 3, 1, 29.90, 0.10, 29.80))
        
        print("✅ Vendas de exemplo criadas")
    
    conn.commit()
    conn.close()

def get_db():
    """Obter conexão com banco"""
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    return conn

def login_required(f):
    """Decorator para verificar login"""
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    decorated_function.__name__ = f.__name__
    return decorated_function

# Rotas principais
@app.route('/')
@login_required
def index():
    """Dashboard principal"""
    conn = get_db()
    cursor = conn.cursor()
    
    # Estatísticas
    cursor.execute("SELECT COUNT(*) as total FROM produtos WHERE ativo = 1")
    total_produtos = cursor.fetchone()['total']
    
    cursor.execute("SELECT COUNT(*) as total FROM clientes WHERE ativo = 1")
    total_clientes = cursor.fetchone()['total']
    
    cursor.execute("SELECT COUNT(*) as total FROM vendas WHERE DATE(data_venda) = DATE('now')")
    vendas_hoje = cursor.fetchone()['total']
    
    cursor.execute("SELECT COALESCE(SUM(total_final), 0) as total FROM vendas WHERE DATE(data_venda) = DATE('now')")
    faturamento_hoje = cursor.fetchone()['total']
    
    cursor.execute("SELECT COUNT(*) as total FROM vendas")
    total_vendas = cursor.fetchone()['total']
    
    cursor.execute("SELECT COALESCE(SUM(total_final), 0) as total FROM vendas")
    faturamento_total = cursor.fetchone()['total']
    
    # Produtos com estoque baixo
    cursor.execute("SELECT COUNT(*) as total FROM produtos WHERE estoque_atual <= estoque_minimo AND ativo = 1")
    produtos_estoque_baixo = cursor.fetchone()['total']
    
    # Vendas recentes
    cursor.execute('''
        SELECT v.*, c.nome as cliente_nome, u.nome as vendedor_nome
        FROM vendas v
        JOIN clientes c ON v.cliente_id = c.id
        JOIN usuarios u ON v.vendedor_id = u.id
        ORDER BY v.data_venda DESC
        LIMIT 5
    ''')
    vendas_recentes = [dict(row) for row in cursor.fetchall()]
    
    # Produtos mais vendidos
    cursor.execute('''
        SELECT p.nome, SUM(iv.quantidade) as total_vendido
        FROM itens_venda iv
        JOIN produtos p ON iv.produto_id = p.id
        GROUP BY p.id, p.nome
        ORDER BY total_vendido DESC
        LIMIT 5
    ''')
    produtos_mais_vendidos = [dict(row) for row in cursor.fetchall()]
    
    conn.close()
    
    return render_template('dashboard.html',
                         total_produtos=total_produtos,
                         total_clientes=total_clientes,
                         vendas_hoje=vendas_hoje,
                         faturamento_hoje=faturamento_hoje,
                         total_vendas=total_vendas,
                         faturamento_total=faturamento_total,
                         produtos_estoque_baixo=produtos_estoque_baixo,
                         vendas_recentes=vendas_recentes,
                         produtos_mais_vendidos=produtos_mais_vendidos)

@app.route('/login', methods=['GET', 'POST'])
def login():
    """Página de login"""
    if request.method == 'POST':
        email = request.form['email']
        senha = request.form['senha']

        conn = get_db()
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM usuarios WHERE email = ? AND ativo = 1', (email,))
        usuario = cursor.fetchone()
        conn.close()

        if usuario and verify_password(senha, usuario['senha']):
            session['user_id'] = usuario['id']
            session['user_nome'] = usuario['nome']
            session['user_tipo'] = usuario['tipo']
            session['user_email'] = usuario['email']

            flash('Login realizado com sucesso!', 'success')
            return redirect(url_for('index'))
        else:
            flash('Email ou senha incorretos!', 'error')

    return render_template('login.html')

@app.route('/logout')
def logout():
    """Logout"""
    session.clear()
    flash('Logout realizado com sucesso!', 'success')
    return redirect(url_for('login'))

@app.route('/produtos')
@login_required
def produtos():
    """Lista de produtos"""
    conn = get_db()
    cursor = conn.cursor()
    cursor.execute('''
        SELECT p.*, c.nome as categoria_nome
        FROM produtos p
        LEFT JOIN categorias c ON p.categoria_id = c.id
        WHERE p.ativo = 1
        ORDER BY p.nome
    ''')
    produtos_lista = [dict(row) for row in cursor.fetchall()]
    conn.close()

    return render_template('produtos/lista.html', produtos=produtos_lista)

@app.route('/clientes')
@login_required
def clientes():
    """Lista de clientes"""
    conn = get_db()
    cursor = conn.cursor()
    cursor.execute('SELECT * FROM clientes WHERE ativo = 1 ORDER BY nome')
    clientes_lista = [dict(row) for row in cursor.fetchall()]
    conn.close()

    return render_template('clientes/lista.html', clientes=clientes_lista)

@app.route('/vendas')
@login_required
def vendas():
    """Lista de vendas"""
    conn = get_db()
    cursor = conn.cursor()
    cursor.execute('''
        SELECT v.*, c.nome as cliente_nome, c.telefone as cliente_telefone,
               u.nome as vendedor_nome
        FROM vendas v
        JOIN clientes c ON v.cliente_id = c.id
        JOIN usuarios u ON v.vendedor_id = u.id
        ORDER BY v.data_venda DESC
    ''')
    vendas_lista = [dict(row) for row in cursor.fetchall()]
    conn.close()

    return render_template('vendas/lista.html', vendas=vendas_lista)

@app.route('/vendas/nova', methods=['GET', 'POST'])
@login_required
def venda_nova():
    """Nova venda"""
    if request.method == 'POST':
        try:
            data = request.get_json()

            cliente_id = data['cliente_id']
            vendedor_id = session['user_id']
            itens = data['itens']
            desconto_total = data.get('desconto_total', 0)
            observacoes = data.get('observacoes')

            # Validações
            if not cliente_id or not itens:
                return jsonify({'success': False, 'message': 'Cliente e itens são obrigatórios'}), 400

            # Criar venda
            conn = get_db()
            cursor = conn.cursor()

            # Calcular subtotal
            subtotal = 0
            for item in itens:
                subtotal += item['preco_unitario'] * item['quantidade']

            # Calcular total final
            desconto_itens = sum(item.get('desconto_item', 0) for item in itens)
            total_final = subtotal - desconto_itens - desconto_total

            # Inserir venda
            cursor.execute('''
                INSERT INTO vendas (cliente_id, vendedor_id, subtotal, desconto_total, total_final, observacoes)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (cliente_id, vendedor_id, subtotal, desconto_total, total_final, observacoes))

            venda_id = cursor.lastrowid

            # Inserir itens da venda
            for item in itens:
                subtotal_item = (item['preco_unitario'] * item['quantidade']) - item.get('desconto_item', 0)

                cursor.execute('''
                    INSERT INTO itens_venda (venda_id, produto_id, quantidade, preco_unitario, desconto_item, subtotal_item)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (venda_id, item['produto_id'], item['quantidade'],
                      item['preco_unitario'], item.get('desconto_item', 0), subtotal_item))

                # Atualizar estoque
                cursor.execute('''
                    UPDATE produtos SET estoque_atual = estoque_atual - ?
                    WHERE id = ?
                ''', (item['quantidade'], item['produto_id']))

            conn.commit()
            conn.close()

            return jsonify({
                'success': True,
                'message': 'Venda realizada com sucesso!',
                'venda_id': venda_id
            })

        except Exception as e:
            return jsonify({'success': False, 'message': str(e)}), 500

    # GET - Mostrar formulário
    conn = get_db()
    cursor = conn.cursor()

    cursor.execute('SELECT * FROM clientes WHERE ativo = 1 ORDER BY nome')
    clientes_lista = [dict(row) for row in cursor.fetchall()]

    cursor.execute('SELECT * FROM produtos WHERE ativo = 1 ORDER BY nome')
    produtos_lista = [dict(row) for row in cursor.fetchall()]

    conn.close()

    return render_template('vendas/form.html',
                         clientes=clientes_lista,
                         produtos=produtos_lista)

def abrir_navegador():
    """Abrir navegador automaticamente"""
    time.sleep(3)
    webbrowser.open(f'http://localhost:{PORT}')

if __name__ == '__main__':
    print("🚀 Iniciando Saúde Flex...")
    print("📁 Inicializando banco de dados...")

    try:
        init_database()
        print("✅ Banco de dados inicializado")
        print("🌐 Iniciando servidor Flask...")
        print(f"📍 Acesse: http://localhost:{PORT}")
        print("🔑 Login: <EMAIL> / admin123")
        print("-" * 50)

        # Abrir navegador automaticamente
        threading.Thread(target=abrir_navegador, daemon=True).start()

        app.run(debug=False, host='127.0.0.1', port=PORT)

    except Exception as e:
        print(f"❌ Erro ao iniciar: {e}")
        import traceback
        traceback.print_exc()
        input("Pressione Enter para sair...")
