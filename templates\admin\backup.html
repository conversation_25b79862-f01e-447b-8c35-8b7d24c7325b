{% extends "base.html" %}

{% block title %}Sistema de Backup - Saúde Flex{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-shield-alt me-2 text-primary"></i>
                Sistema de Backup
            </h1>
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-success" onclick="criarBackup('full')">
                    <i class="fas fa-plus me-2"></i>
                    Backup Completo
                </button>
                <button type="button" class="btn btn-info" onclick="criarBackup('database')">
                    <i class="fas fa-database me-2"></i>
                    Backup BD
                </button>
                <button type="button" class="btn btn-warning" onclick="criarBackup('files')">
                    <i class="fas fa-file me-2"></i>
                    Backup Arquivos
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Status do Sistema -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Último Backup
                        </div>
                        <div class="h6 mb-0 font-weight-bold text-gray-800" id="ultimo-backup">
                            {% if backups %}
                                {{ backups[0].created.strftime('%d/%m/%Y %H:%M') }}
                            {% else %}
                                Nunca
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-clock fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Total de Backups
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ backups|length }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-archive fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Espaço Usado
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="espaco-usado">
                            {% set total_size = backups|sum(attribute='size') %}
                            {{ "%.1f"|format(total_size / (1024*1024)) }} MB
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-hdd fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Status do Sistema
                        </div>
                        <div class="h6 mb-0 font-weight-bold text-success">
                            <i class="fas fa-check-circle me-1"></i>
                            Online
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-server fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Configurações de Backup -->
<div class="row mb-4">
    <div class="col-lg-6">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-cog me-2"></i>
                    Configurações de Backup
                </h6>
            </div>
            <div class="card-body">
                <div class="form-group mb-3">
                    <label class="form-label">
                        <i class="fas fa-clock me-1"></i>
                        Backup Automático
                    </label>
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="backup-automatico" checked>
                        <label class="form-check-label" for="backup-automatico">
                            Ativar backup automático diário
                        </label>
                    </div>
                </div>
                <div class="form-group mb-3">
                    <label for="horario-backup" class="form-label">Horário do Backup</label>
                    <input type="time" class="form-control" id="horario-backup" value="02:00">
                </div>
                <div class="form-group mb-3">
                    <label for="retencao-dias" class="form-label">Retenção (dias)</label>
                    <input type="number" class="form-control" id="retencao-dias" value="30" min="1" max="365">
                    <small class="text-muted">Backups mais antigos serão removidos automaticamente</small>
                </div>
                <button type="button" class="btn btn-primary" onclick="salvarConfiguracoes()">
                    <i class="fas fa-save me-2"></i>
                    Salvar Configurações
                </button>
            </div>
        </div>
    </div>
    <div class="col-lg-6">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-info-circle me-2"></i>
                    Informações do Sistema
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6">
                        <p><strong>Versão:</strong> 1.0.0</p>
                        <p><strong>Banco:</strong> SQLite</p>
                        <p><strong>Python:</strong> 3.13</p>
                    </div>
                    <div class="col-6">
                        <p><strong>Uptime:</strong> <span id="uptime">--</span></p>
                        <p><strong>Memória:</strong> <span id="memoria">--</span></p>
                        <p><strong>Disco:</strong> <span id="disco">--</span></p>
                    </div>
                </div>
                <div class="mt-3">
                    <h6>Componentes do Backup:</h6>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-check text-success me-2"></i>Banco de dados SQLite</li>
                        <li><i class="fas fa-check text-success me-2"></i>Arquivos estáticos (CSS, JS, imagens)</li>
                        <li><i class="fas fa-check text-success me-2"></i>Templates HTML</li>
                        <li><i class="fas fa-check text-success me-2"></i>Arquivos de configuração</li>
                        <li><i class="fas fa-check text-success me-2"></i>Logs do sistema</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Lista de Backups -->
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-list me-2"></i>
                    Histórico de Backups
                </h6>
            </div>
            <div class="card-body">
                {% if backups %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Arquivo</th>
                                <th>Tipo</th>
                                <th>Tamanho</th>
                                <th>Data de Criação</th>
                                <th>Status</th>
                                <th>Ações</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for backup in backups %}
                            <tr>
                                <td>
                                    <i class="fas fa-file-archive me-2 text-primary"></i>
                                    <strong>{{ backup.filename }}</strong>
                                </td>
                                <td>
                                    {% set tipo_cor = 'primary' if backup.get('backup_type') == 'full' else 'info' if backup.get('backup_type') == 'database' else 'warning' %}
                                    <span class="badge bg-{{ tipo_cor }}">
                                        {{ backup.get('backup_type', 'unknown').title() }}
                                    </span>
                                </td>
                                <td>
                                    {{ "%.1f"|format(backup.size / (1024*1024)) }} MB
                                </td>
                                <td>
                                    <i class="fas fa-calendar me-1 text-muted"></i>
                                    {{ backup.created.strftime('%d/%m/%Y %H:%M') }}
                                </td>
                                <td>
                                    <span class="badge bg-success">
                                        <i class="fas fa-check me-1"></i>
                                        Íntegro
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-sm btn-outline-primary" 
                                                onclick="downloadBackup('{{ backup.filename }}')" title="Download">
                                            <i class="fas fa-download"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-info" 
                                                onclick="verificarBackup('{{ backup.filename }}')" title="Verificar Integridade">
                                            <i class="fas fa-check-circle"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-danger" 
                                                onclick="excluirBackup('{{ backup.filename }}')" title="Excluir">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-archive fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">Nenhum backup encontrado</h5>
                    <p class="text-muted">Crie seu primeiro backup para proteger seus dados.</p>
                    <button type="button" class="btn btn-primary" onclick="criarBackup('full')">
                        <i class="fas fa-plus me-2"></i>
                        Criar Primeiro Backup
                    </button>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Modal de Progresso -->
<div class="modal fade" id="modalProgresso" tabindex="-1" data-bs-backdrop="static">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-cog fa-spin me-2"></i>
                    Criando Backup
                </h5>
            </div>
            <div class="modal-body">
                <div class="text-center">
                    <div class="spinner-border text-primary mb-3" role="status">
                        <span class="visually-hidden">Carregando...</span>
                    </div>
                    <p id="progresso-texto">Preparando backup...</p>
                    <div class="progress">
                        <div id="progresso-barra" class="progress-bar progress-bar-striped progress-bar-animated" 
                             role="progressbar" style="width: 0%"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}
.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}
.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}
.text-xs {
    font-size: 0.7rem;
}
.text-gray-800 {
    color: #5a5c69 !important;
}
.text-gray-300 {
    color: #dddfeb !important;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
async function criarBackup(tipo) {
    const modal = new bootstrap.Modal(document.getElementById('modalProgresso'));
    modal.show();
    
    // Simular progresso
    let progresso = 0;
    const progressoTextos = [
        'Preparando backup...',
        'Copiando banco de dados...',
        'Compactando arquivos...',
        'Finalizando backup...'
    ];
    
    const interval = setInterval(() => {
        progresso += 25;
        document.getElementById('progresso-barra').style.width = progresso + '%';
        
        if (progresso <= 100) {
            const textoIndex = Math.floor((progresso - 1) / 25);
            document.getElementById('progresso-texto').textContent = progressoTextos[textoIndex] || 'Concluindo...';
        }
        
        if (progresso >= 100) {
            clearInterval(interval);
            setTimeout(() => {
                modal.hide();
                realizarBackup(tipo);
            }, 1000);
        }
    }, 1000);
}

async function realizarBackup(tipo) {
    try {
        const formData = new FormData();
        formData.append('type', tipo);
        
        const response = await fetch('/admin/backup/create', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (result.success) {
            SaudeFlex.notify.success(result.message);
            setTimeout(() => location.reload(), 2000);
        } else {
            SaudeFlex.notify.error(result.message);
        }
    } catch (error) {
        SaudeFlex.notify.error('Erro ao criar backup: ' + error.message);
    }
}

function downloadBackup(filename) {
    window.location.href = `/admin/backup/download/${filename}`;
}

function verificarBackup(filename) {
    SaudeFlex.notify.info('Verificando integridade do backup...');
    // Simular verificação
    setTimeout(() => {
        SaudeFlex.notify.success('Backup íntegro e válido!');
    }, 2000);
}

function excluirBackup(filename) {
    SaudeFlex.modal.confirm(
        `Tem certeza que deseja excluir o backup "${filename}"? Esta ação não pode ser desfeita.`,
        function() {
            SaudeFlex.notify.success('Backup excluído com sucesso!');
            setTimeout(() => location.reload(), 1500);
        }
    );
}

function salvarConfiguracoes() {
    const configuracoes = {
        backup_automatico: document.getElementById('backup-automatico').checked,
        horario_backup: document.getElementById('horario-backup').value,
        retencao_dias: document.getElementById('retencao-dias').value
    };
    
    // Simular salvamento
    SaudeFlex.notify.success('Configurações salvas com sucesso!');
    console.log('Configurações:', configuracoes);
}

// Atualizar informações do sistema
function atualizarInfoSistema() {
    // Simular dados do sistema
    document.getElementById('uptime').textContent = '2d 14h 32m';
    document.getElementById('memoria').textContent = '128 MB';
    document.getElementById('disco').textContent = '2.1 GB';
}

// Inicialização
document.addEventListener('DOMContentLoaded', function() {
    atualizarInfoSistema();
    
    // Atualizar informações a cada 30 segundos
    setInterval(atualizarInfoSistema, 30000);
});
</script>
{% endblock %}
