#!/usr/bin/env python3
"""
Sistema de Backup Automático - Saúde Flex
Realiza backup do banco de dados e arquivos importantes
"""

import os
import shutil
import sqlite3
import zipfile
from datetime import datetime
import json
import logging

# Configuração de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('backup.log'),
        logging.StreamHandler()
    ]
)

class BackupSystem:
    def __init__(self):
        self.backup_dir = 'backups'
        self.db_file = 'saude_flex.db'
        self.static_dir = 'static'
        self.templates_dir = 'templates'
        
        # Criar diretório de backup se não existir
        if not os.path.exists(self.backup_dir):
            os.makedirs(self.backup_dir)
    
    def create_backup(self, backup_type='full'):
        """
        Criar backup do sistema
        
        Args:
            backup_type (str): Tipo de backup ('full', 'database', 'files')
        
        Returns:
            str: Caminho do arquivo de backup criado
        """
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_name = f'saude_flex_backup_{backup_type}_{timestamp}'
        
        try:
            if backup_type == 'full':
                return self._create_full_backup(backup_name)
            elif backup_type == 'database':
                return self._create_database_backup(backup_name)
            elif backup_type == 'files':
                return self._create_files_backup(backup_name)
            else:
                raise ValueError(f"Tipo de backup inválido: {backup_type}")
                
        except Exception as e:
            logging.error(f"Erro ao criar backup: {str(e)}")
            raise
    
    def _create_full_backup(self, backup_name):
        """Criar backup completo do sistema"""
        backup_path = os.path.join(self.backup_dir, f'{backup_name}.zip')
        
        with zipfile.ZipFile(backup_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            # Backup do banco de dados
            if os.path.exists(self.db_file):
                # Criar backup SQL do banco
                sql_backup = self._export_database_to_sql()
                zipf.writestr(f'{backup_name}_database.sql', sql_backup)
                
                # Adicionar arquivo do banco
                zipf.write(self.db_file, f'database/{self.db_file}')
            
            # Backup dos arquivos estáticos
            if os.path.exists(self.static_dir):
                for root, dirs, files in os.walk(self.static_dir):
                    for file in files:
                        file_path = os.path.join(root, file)
                        arcname = os.path.relpath(file_path, '.')
                        zipf.write(file_path, arcname)
            
            # Backup dos templates
            if os.path.exists(self.templates_dir):
                for root, dirs, files in os.walk(self.templates_dir):
                    for file in files:
                        file_path = os.path.join(root, file)
                        arcname = os.path.relpath(file_path, '.')
                        zipf.write(file_path, arcname)
            
            # Backup dos arquivos de configuração
            config_files = ['app.py', 'database.py', 'models.py', 'requirements.txt', 'README.md']
            for config_file in config_files:
                if os.path.exists(config_file):
                    zipf.write(config_file)
            
            # Adicionar metadados do backup
            metadata = {
                'backup_type': 'full',
                'timestamp': datetime.now().isoformat(),
                'version': '1.0',
                'files_count': len(zipf.namelist())
            }
            zipf.writestr('backup_metadata.json', json.dumps(metadata, indent=2))
        
        logging.info(f"Backup completo criado: {backup_path}")
        return backup_path
    
    def _create_database_backup(self, backup_name):
        """Criar backup apenas do banco de dados"""
        backup_path = os.path.join(self.backup_dir, f'{backup_name}.zip')
        
        with zipfile.ZipFile(backup_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            # Backup SQL
            sql_backup = self._export_database_to_sql()
            zipf.writestr(f'{backup_name}.sql', sql_backup)
            
            # Arquivo do banco
            if os.path.exists(self.db_file):
                zipf.write(self.db_file, f'database/{self.db_file}')
            
            # Metadados
            metadata = {
                'backup_type': 'database',
                'timestamp': datetime.now().isoformat(),
                'version': '1.0'
            }
            zipf.writestr('backup_metadata.json', json.dumps(metadata, indent=2))
        
        logging.info(f"Backup do banco de dados criado: {backup_path}")
        return backup_path
    
    def _create_files_backup(self, backup_name):
        """Criar backup apenas dos arquivos"""
        backup_path = os.path.join(self.backup_dir, f'{backup_name}.zip')
        
        with zipfile.ZipFile(backup_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            # Arquivos estáticos
            if os.path.exists(self.static_dir):
                for root, dirs, files in os.walk(self.static_dir):
                    for file in files:
                        file_path = os.path.join(root, file)
                        arcname = os.path.relpath(file_path, '.')
                        zipf.write(file_path, arcname)
            
            # Templates
            if os.path.exists(self.templates_dir):
                for root, dirs, files in os.walk(self.templates_dir):
                    for file in files:
                        file_path = os.path.join(root, file)
                        arcname = os.path.relpath(file_path, '.')
                        zipf.write(file_path, arcname)
            
            # Metadados
            metadata = {
                'backup_type': 'files',
                'timestamp': datetime.now().isoformat(),
                'version': '1.0'
            }
            zipf.writestr('backup_metadata.json', json.dumps(metadata, indent=2))
        
        logging.info(f"Backup de arquivos criado: {backup_path}")
        return backup_path
    
    def _export_database_to_sql(self):
        """Exportar banco de dados para SQL"""
        if not os.path.exists(self.db_file):
            return "-- Banco de dados não encontrado"
        
        conn = sqlite3.connect(self.db_file)
        
        # Obter script SQL completo
        sql_script = ""
        for line in conn.iterdump():
            sql_script += line + '\n'
        
        conn.close()
        return sql_script
    
    def list_backups(self):
        """Listar todos os backups disponíveis"""
        backups = []
        
        if not os.path.exists(self.backup_dir):
            return backups
        
        for file in os.listdir(self.backup_dir):
            if file.endswith('.zip'):
                file_path = os.path.join(self.backup_dir, file)
                stat = os.stat(file_path)
                
                backup_info = {
                    'filename': file,
                    'path': file_path,
                    'size': stat.st_size,
                    'created': datetime.fromtimestamp(stat.st_ctime),
                    'modified': datetime.fromtimestamp(stat.st_mtime)
                }
                
                # Tentar extrair metadados
                try:
                    with zipfile.ZipFile(file_path, 'r') as zipf:
                        if 'backup_metadata.json' in zipf.namelist():
                            metadata_content = zipf.read('backup_metadata.json').decode('utf-8')
                            metadata = json.loads(metadata_content)
                            backup_info.update(metadata)
                except:
                    pass
                
                backups.append(backup_info)
        
        # Ordenar por data de criação (mais recente primeiro)
        backups.sort(key=lambda x: x['created'], reverse=True)
        return backups
    
    def restore_backup(self, backup_path, restore_type='full'):
        """
        Restaurar backup
        
        Args:
            backup_path (str): Caminho do arquivo de backup
            restore_type (str): Tipo de restauração ('full', 'database', 'files')
        """
        if not os.path.exists(backup_path):
            raise FileNotFoundError(f"Arquivo de backup não encontrado: {backup_path}")
        
        try:
            with zipfile.ZipFile(backup_path, 'r') as zipf:
                if restore_type == 'full':
                    # Restaurar tudo
                    zipf.extractall('.')
                elif restore_type == 'database':
                    # Restaurar apenas banco de dados
                    for file in zipf.namelist():
                        if file.endswith('.db') or file.endswith('.sql'):
                            zipf.extract(file, '.')
                elif restore_type == 'files':
                    # Restaurar apenas arquivos
                    for file in zipf.namelist():
                        if file.startswith(('static/', 'templates/')):
                            zipf.extract(file, '.')
            
            logging.info(f"Backup restaurado com sucesso: {backup_path}")
            
        except Exception as e:
            logging.error(f"Erro ao restaurar backup: {str(e)}")
            raise
    
    def cleanup_old_backups(self, keep_days=30):
        """Limpar backups antigos"""
        if not os.path.exists(self.backup_dir):
            return
        
        cutoff_date = datetime.now().timestamp() - (keep_days * 24 * 60 * 60)
        removed_count = 0
        
        for file in os.listdir(self.backup_dir):
            if file.endswith('.zip'):
                file_path = os.path.join(self.backup_dir, file)
                if os.path.getctime(file_path) < cutoff_date:
                    os.remove(file_path)
                    removed_count += 1
                    logging.info(f"Backup antigo removido: {file}")
        
        logging.info(f"Limpeza concluída. {removed_count} backup(s) removido(s).")

def main():
    """Função principal para execução via linha de comando"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Sistema de Backup Saúde Flex')
    parser.add_argument('action', choices=['create', 'list', 'restore', 'cleanup'], 
                       help='Ação a ser executada')
    parser.add_argument('--type', choices=['full', 'database', 'files'], 
                       default='full', help='Tipo de backup')
    parser.add_argument('--file', help='Arquivo de backup para restauração')
    parser.add_argument('--days', type=int, default=30, 
                       help='Dias para manter backups (cleanup)')
    
    args = parser.parse_args()
    
    backup_system = BackupSystem()
    
    if args.action == 'create':
        backup_path = backup_system.create_backup(args.type)
        print(f"Backup criado: {backup_path}")
    
    elif args.action == 'list':
        backups = backup_system.list_backups()
        print(f"{'Arquivo':<40} {'Tipo':<10} {'Tamanho':<10} {'Data'}")
        print("-" * 80)
        for backup in backups:
            size_mb = backup['size'] / (1024 * 1024)
            backup_type = backup.get('backup_type', 'unknown')
            date_str = backup['created'].strftime('%d/%m/%Y %H:%M')
            print(f"{backup['filename']:<40} {backup_type:<10} {size_mb:.1f}MB {date_str}")
    
    elif args.action == 'restore':
        if not args.file:
            print("Erro: --file é obrigatório para restauração")
            return
        backup_system.restore_backup(args.file, args.type)
        print(f"Backup restaurado: {args.file}")
    
    elif args.action == 'cleanup':
        backup_system.cleanup_old_backups(args.days)
        print(f"Limpeza concluída. Backups com mais de {args.days} dias foram removidos.")

if __name__ == '__main__':
    main()
