{% extends "base.html" %}

{% block title %}Usuários - Saúde Flex{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-user-cog me-2 text-primary"></i>
                Gerenciamento de Usuários
            </h1>
            <a href="{{ url_for('usuario_novo') }}" class="btn btn-primary">
                <i class="fas fa-user-plus me-2"></i>
                Novo Usuário
            </a>
        </div>
    </div>
</div>

<!-- Resumo de Usuários -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Total de Usuários
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ usuarios|length }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Administradores
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="total-admins">
                            0
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-user-shield fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Gerentes
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="total-gerentes">
                            0
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-user-tie fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Vendedores
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="total-vendedores">
                            0
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-user fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Lista de Usuários -->
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-list me-2"></i>
                    Lista de Usuários
                </h6>
            </div>
            <div class="card-body">
                {% if usuarios %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Nome</th>
                                <th>Email</th>
                                <th>Tipo</th>
                                <th>Status</th>
                                <th>Data Criação</th>
                                <th>Ações</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for usuario in usuarios %}
                            <tr data-tipo="{{ usuario.tipo }}">
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="bg-{{ 'primary' if usuario.tipo == 'admin' else 'info' if usuario.tipo == 'gerente' else 'warning' }} rounded-circle d-flex align-items-center justify-content-center me-3" 
                                             style="width: 40px; height: 40px;">
                                            <i class="fas fa-{{ 'user-shield' if usuario.tipo == 'admin' else 'user-tie' if usuario.tipo == 'gerente' else 'user' }} text-white"></i>
                                        </div>
                                        <div>
                                            <strong>{{ usuario.nome }}</strong>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <i class="fas fa-envelope me-2 text-muted"></i>
                                    {{ usuario.email }}
                                </td>
                                <td>
                                    <span class="badge bg-{{ 'primary' if usuario.tipo == 'admin' else 'info' if usuario.tipo == 'gerente' else 'warning' }}">
                                        {{ usuario.tipo.title() }}
                                    </span>
                                </td>
                                <td>
                                    {% if usuario.ativo %}
                                    <span class="badge bg-success">
                                        <i class="fas fa-check me-1"></i>
                                        Ativo
                                    </span>
                                    {% else %}
                                    <span class="badge bg-danger">
                                        <i class="fas fa-times me-1"></i>
                                        Inativo
                                    </span>
                                    {% endif %}
                                </td>
                                <td>
                                    <i class="fas fa-calendar me-2 text-muted"></i>
                                    {{ usuario.data_criacao }}
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-sm btn-outline-primary" 
                                                onclick="verUsuario({{ usuario.id }})" title="Ver Detalhes">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-warning" 
                                                onclick="editarUsuario({{ usuario.id }})" title="Editar">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        {% if usuario.id != session.user_id %}
                                        <button type="button" class="btn btn-sm btn-outline-{{ 'success' if not usuario.ativo else 'secondary' }}" 
                                                onclick="alterarStatus({{ usuario.id }}, {{ 'true' if not usuario.ativo else 'false' }})" 
                                                title="{{ 'Ativar' if not usuario.ativo else 'Desativar' }}">
                                            <i class="fas fa-{{ 'check' if not usuario.ativo else 'pause' }}"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-danger" 
                                                onclick="excluirUsuario({{ usuario.id }})" title="Excluir">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">Nenhum usuário cadastrado</h5>
                    <p class="text-muted">Comece cadastrando o primeiro usuário.</p>
                    <a href="{{ url_for('usuario_novo') }}" class="btn btn-primary">
                        <i class="fas fa-user-plus me-2"></i>
                        Cadastrar Primeiro Usuário
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Modal de Detalhes do Usuário -->
<div class="modal fade" id="modalDetalhesUsuario" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-user me-2"></i>
                    Detalhes do Usuário
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="conteudo-detalhes-usuario">
                <!-- Conteúdo será carregado via JavaScript -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
                <button type="button" class="btn btn-warning" onclick="editarUsuarioModal()">
                    <i class="fas fa-edit me-2"></i>
                    Editar
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}
.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}
.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}
.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}
.text-xs {
    font-size: 0.7rem;
}
.text-gray-800 {
    color: #5a5c69 !important;
}
.text-gray-300 {
    color: #dddfeb !important;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
let usuarioAtual = null;

function verUsuario(id) {
    usuarioAtual = id;
    
    // Simular carregamento de dados do usuário
    const conteudo = `
        <div class="text-center mb-3">
            <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center mx-auto" 
                 style="width: 80px; height: 80px;">
                <i class="fas fa-user fa-2x text-white"></i>
            </div>
        </div>
        <div class="row">
            <div class="col-12">
                <p><strong>Nome:</strong> Usuário Exemplo</p>
                <p><strong>Email:</strong> <EMAIL></p>
                <p><strong>Tipo:</strong> <span class="badge bg-primary">Admin</span></p>
                <p><strong>Status:</strong> <span class="badge bg-success">Ativo</span></p>
                <p><strong>Data de Criação:</strong> 15/06/2024</p>
                <p><strong>Último Acesso:</strong> Hoje às 14:30</p>
            </div>
        </div>
    `;
    
    document.getElementById('conteudo-detalhes-usuario').innerHTML = conteudo;
    
    const modal = new bootstrap.Modal(document.getElementById('modalDetalhesUsuario'));
    modal.show();
}

function editarUsuario(id) {
    window.location.href = '/usuarios/editar/' + id;
}

function editarUsuarioModal() {
    if (usuarioAtual) {
        editarUsuario(usuarioAtual);
    }
}

function alterarStatus(id, ativar) {
    const acao = ativar ? 'ativar' : 'desativar';
    
    SaudeFlex.modal.confirm(
        `Tem certeza que deseja ${acao} este usuário?`,
        function() {
            // Implementar alteração de status
            SaudeFlex.notify.success(`Usuário ${acao}do com sucesso!`);
            location.reload();
        }
    );
}

function excluirUsuario(id) {
    SaudeFlex.modal.confirm(
        'Tem certeza que deseja excluir este usuário? Esta ação não pode ser desfeita.',
        function() {
            // Implementar exclusão
            SaudeFlex.notify.success('Usuário excluído com sucesso!');
            location.reload();
        }
    );
}

// Contar usuários por tipo
function contarUsuariosPorTipo() {
    const linhas = document.querySelectorAll('tbody tr[data-tipo]');
    let admins = 0, gerentes = 0, vendedores = 0;
    
    linhas.forEach(linha => {
        const tipo = linha.dataset.tipo;
        if (tipo === 'admin') admins++;
        else if (tipo === 'gerente') gerentes++;
        else if (tipo === 'vendedor') vendedores++;
    });
    
    document.getElementById('total-admins').textContent = admins;
    document.getElementById('total-gerentes').textContent = gerentes;
    document.getElementById('total-vendedores').textContent = vendedores;
}

// Inicialização
document.addEventListener('DOMContentLoaded', function() {
    contarUsuariosPorTipo();
});
</script>
{% endblock %}
