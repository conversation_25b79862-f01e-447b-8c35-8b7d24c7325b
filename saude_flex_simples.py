#!/usr/bin/env python3
"""
Saúde Flex - Sistema Simples e Funcional
"""

from flask import Flask, render_template_string, request, redirect, url_for, session, flash, jsonify
from datetime import datetime
import sqlite3
import hashlib
import webbrowser
import threading
import time

# Configurações
DATABASE = 'saude_flex_simples.db'
SECRET_KEY = 'saude_flex_secret_key_2024'
PORT = 5000

# Criar app Flask
app = Flask(__name__)
app.secret_key = SECRET_KEY

def hash_password(password):
    """Hash simples da senha"""
    return hashlib.sha256(password.encode()).hexdigest()

def verify_password(password, hashed):
    """Verificar senha"""
    return hashlib.sha256(password.encode()).hexdigest() == hashed

def init_database():
    """Inicializar banco de dados"""
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    # Tabela de usuários
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS usuarios (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            nome TEXT NOT NULL,
            email TEXT UNIQUE NOT NULL,
            senha TEXT NOT NULL,
            tipo TEXT NOT NULL DEFAULT 'admin'
        )
    ''')
    
    # Tabela de produtos
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS produtos (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            nome TEXT NOT NULL,
            descricao TEXT,
            preco_custo REAL NOT NULL,
            preco_venda REAL NOT NULL,
            estoque_atual INTEGER DEFAULT 0
        )
    ''')
    
    # Tabela de clientes
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS clientes (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            nome TEXT NOT NULL,
            email TEXT,
            telefone TEXT,
            cidade TEXT
        )
    ''')
    
    # Tabela de vendas
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS vendas (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            cliente_id INTEGER NOT NULL,
            vendedor_id INTEGER NOT NULL,
            total_final REAL NOT NULL,
            data_venda TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # Verificar se já existe um admin
    cursor.execute("SELECT id FROM usuarios WHERE tipo = 'admin' LIMIT 1")
    if not cursor.fetchone():
        senha_hash = hash_password('admin123')
        cursor.execute('''
            INSERT INTO usuarios (nome, email, senha, tipo)
            VALUES (?, ?, ?, ?)
        ''', ('Administrador', '<EMAIL>', senha_hash, 'admin'))
        print("✅ Usuário admin criado: <EMAIL> / admin123")
    
    # Criar dados de exemplo
    cursor.execute("SELECT COUNT(*) as total FROM produtos")
    if cursor.fetchone()['total'] == 0:
        produtos_exemplo = [
            ('Whey Protein 1kg', 'Proteína em pó sabor chocolate', 50.00, 89.90, 20),
            ('Creatina 300g', 'Creatina monohidratada pura', 30.00, 59.90, 15),
            ('Shampoo Hidratante', 'Shampoo para cabelos secos', 15.00, 29.90, 25),
            ('Halteres 5kg', 'Par de halteres emborrachados', 80.00, 149.90, 8)
        ]
        
        for produto in produtos_exemplo:
            cursor.execute('''
                INSERT INTO produtos (nome, descricao, preco_custo, preco_venda, estoque_atual)
                VALUES (?, ?, ?, ?, ?)
            ''', produto)
        
        print("✅ Produtos de exemplo criados")
    
    cursor.execute("SELECT COUNT(*) as total FROM clientes")
    if cursor.fetchone()['total'] == 0:
        clientes_exemplo = [
            ('João Silva', '<EMAIL>', '(11) 99999-9999', 'São Paulo'),
            ('Maria Santos', '<EMAIL>', '(11) 88888-8888', 'Rio de Janeiro'),
            ('Pedro Costa', '<EMAIL>', '(11) 77777-7777', 'Belo Horizonte')
        ]
        
        for cliente in clientes_exemplo:
            cursor.execute('''
                INSERT INTO clientes (nome, email, telefone, cidade)
                VALUES (?, ?, ?, ?)
            ''', cliente)
        
        print("✅ Clientes de exemplo criados")
    
    # Criar vendas de exemplo
    cursor.execute("SELECT COUNT(*) as total FROM vendas")
    if cursor.fetchone()['total'] == 0:
        vendas_exemplo = [
            (1, 1, 149.80),  # João comprou
            (2, 1, 179.70),  # Maria comprou
            (3, 1, 89.90)    # Pedro comprou
        ]
        
        for venda in vendas_exemplo:
            cursor.execute('''
                INSERT INTO vendas (cliente_id, vendedor_id, total_final)
                VALUES (?, ?, ?)
            ''', venda)
        
        print("✅ Vendas de exemplo criadas")
    
    conn.commit()
    conn.close()

def get_db():
    """Obter conexão com banco"""
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    return conn

def login_required(f):
    """Decorator para verificar login"""
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    decorated_function.__name__ = f.__name__
    return decorated_function

# Rotas
@app.route('/')
@login_required
def index():
    """Dashboard principal"""
    conn = get_db()
    cursor = conn.cursor()
    
    # Estatísticas
    cursor.execute("SELECT COUNT(*) as total FROM produtos")
    total_produtos = cursor.fetchone()['total']
    
    cursor.execute("SELECT COUNT(*) as total FROM clientes")
    total_clientes = cursor.fetchone()['total']
    
    cursor.execute("SELECT COUNT(*) as total FROM vendas")
    total_vendas = cursor.fetchone()['total']
    
    cursor.execute("SELECT COALESCE(SUM(total_final), 0) as total FROM vendas")
    faturamento_total = cursor.fetchone()['total']
    
    # Produtos recentes
    cursor.execute("SELECT * FROM produtos ORDER BY id DESC LIMIT 5")
    produtos_recentes = [dict(row) for row in cursor.fetchall()]
    
    # Clientes recentes
    cursor.execute("SELECT * FROM clientes ORDER BY id DESC LIMIT 5")
    clientes_recentes = [dict(row) for row in cursor.fetchall()]
    
    # Vendas recentes
    cursor.execute('''
        SELECT v.*, c.nome as cliente_nome, u.nome as vendedor_nome
        FROM vendas v
        JOIN clientes c ON v.cliente_id = c.id
        JOIN usuarios u ON v.vendedor_id = u.id
        ORDER BY v.data_venda DESC
        LIMIT 5
    ''')
    vendas_recentes = [dict(row) for row in cursor.fetchall()]
    
    conn.close()
    
    return render_template_string(TEMPLATE_DASHBOARD,
                                total_produtos=total_produtos,
                                total_clientes=total_clientes,
                                total_vendas=total_vendas,
                                faturamento_total=faturamento_total,
                                produtos_recentes=produtos_recentes,
                                clientes_recentes=clientes_recentes,
                                vendas_recentes=vendas_recentes)

@app.route('/login', methods=['GET', 'POST'])
def login():
    """Página de login"""
    if request.method == 'POST':
        email = request.form['email']
        senha = request.form['senha']
        
        conn = get_db()
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM usuarios WHERE email = ?', (email,))
        usuario = cursor.fetchone()
        conn.close()
        
        if usuario and verify_password(senha, usuario['senha']):
            session['user_id'] = usuario['id']
            session['user_nome'] = usuario['nome']
            session['user_tipo'] = usuario['tipo']
            session['user_email'] = usuario['email']
            
            flash('Login realizado com sucesso!', 'success')
            return redirect(url_for('index'))
        else:
            flash('Email ou senha incorretos!', 'error')
    
    return render_template_string(TEMPLATE_LOGIN)

@app.route('/logout')
def logout():
    """Logout"""
    session.clear()
    flash('Logout realizado com sucesso!', 'success')
    return redirect(url_for('login'))

@app.route('/produtos')
@login_required
def produtos():
    """Lista de produtos"""
    conn = get_db()
    cursor = conn.cursor()
    cursor.execute('SELECT * FROM produtos ORDER BY nome')
    produtos_lista = [dict(row) for row in cursor.fetchall()]
    conn.close()
    
    return render_template_string(TEMPLATE_PRODUTOS, produtos=produtos_lista)

@app.route('/clientes')
@login_required
def clientes():
    """Lista de clientes"""
    conn = get_db()
    cursor = conn.cursor()
    cursor.execute('SELECT * FROM clientes ORDER BY nome')
    clientes_lista = [dict(row) for row in cursor.fetchall()]
    conn.close()
    
    return render_template_string(TEMPLATE_CLIENTES, clientes=clientes_lista)

@app.route('/vendas')
@login_required
def vendas():
    """Lista de vendas"""
    conn = get_db()
    cursor = conn.cursor()
    cursor.execute('''
        SELECT v.*, c.nome as cliente_nome, u.nome as vendedor_nome
        FROM vendas v
        JOIN clientes c ON v.cliente_id = c.id
        JOIN usuarios u ON v.vendedor_id = u.id
        ORDER BY v.data_venda DESC
    ''')
    vendas_lista = [dict(row) for row in cursor.fetchall()]
    conn.close()
    
    return render_template_string(TEMPLATE_VENDAS, vendas=vendas_lista)

def abrir_navegador():
    """Abrir navegador automaticamente"""
    time.sleep(3)
    webbrowser.open(f'http://localhost:{PORT}')

# Templates inline
TEMPLATE_LOGIN = '''
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Saúde Flex</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container">
        <div class="row justify-content-center mt-5">
            <div class="col-md-6">
                <div class="card shadow">
                    <div class="card-body">
                        <div class="text-center mb-4">
                            <i class="fas fa-heartbeat fa-3x text-primary"></i>
                            <h3 class="mt-2">Saúde Flex</h3>
                            <p class="text-muted">Sistema de Agendamentos e Vendas</p>
                        </div>
                        
                        {% with messages = get_flashed_messages(with_categories=true) %}
                            {% if messages %}
                                {% for category, message in messages %}
                                    <div class="alert alert-{{ 'danger' if category == 'error' else 'success' }} alert-dismissible fade show">
                                        {{ message }}
                                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                    </div>
                                {% endfor %}
                            {% endif %}
                        {% endwith %}
                        
                        <form method="POST">
                            <div class="mb-3">
                                <label for="email" class="form-label">Email</label>
                                <input type="email" class="form-control" id="email" name="email" required>
                            </div>
                            <div class="mb-3">
                                <label for="senha" class="form-label">Senha</label>
                                <input type="password" class="form-control" id="senha" name="senha" required>
                            </div>
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-sign-in-alt me-2"></i>Entrar
                            </button>
                        </form>
                        
                        <div class="mt-4 text-center">
                            <small class="text-muted">
                                <strong>Login padrão:</strong><br>
                                Email: <EMAIL><br>
                                Senha: admin123
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
'''

TEMPLATE_DASHBOARD = '''
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Saúde Flex</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="fas fa-heartbeat me-2"></i>Saúde Flex
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('produtos') }}">
                    <i class="fas fa-box me-1"></i>Produtos
                </a>
                <a class="nav-link" href="{{ url_for('clientes') }}">
                    <i class="fas fa-users me-1"></i>Clientes
                </a>
                <a class="nav-link" href="{{ url_for('vendas') }}">
                    <i class="fas fa-shopping-cart me-1"></i>Vendas
                </a>
                <span class="navbar-text me-3">
                    <i class="fas fa-user me-1"></i>{{ session.user_nome }}
                </span>
                <a class="nav-link" href="{{ url_for('logout') }}">
                    <i class="fas fa-sign-out-alt me-1"></i>Sair
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <h1 class="h3 mb-4">
                    <i class="fas fa-tachometer-alt me-2 text-primary"></i>
                    Dashboard - Saúde Flex
                </h1>
            </div>
        </div>

        <!-- Estatísticas -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-start border-primary border-4 shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs fw-bold text-primary text-uppercase mb-1">
                                    Total de Produtos
                                </div>
                                <div class="h5 mb-0 fw-bold text-gray-800">
                                    {{ total_produtos }}
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-box fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-start border-success border-4 shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs fw-bold text-success text-uppercase mb-1">
                                    Total de Clientes
                                </div>
                                <div class="h5 mb-0 fw-bold text-gray-800">
                                    {{ total_clientes }}
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-users fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-start border-info border-4 shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs fw-bold text-info text-uppercase mb-1">
                                    Total de Vendas
                                </div>
                                <div class="h5 mb-0 fw-bold text-gray-800">
                                    {{ total_vendas }}
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-start border-warning border-4 shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs fw-bold text-warning text-uppercase mb-1">
                                    Faturamento Total
                                </div>
                                <div class="h5 mb-0 fw-bold text-gray-800">
                                    R$ {{ "%.2f"|format(faturamento_total) }}
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Status do Sistema -->
        <div class="row">
            <div class="col-12">
                <div class="alert alert-success">
                    <h5><i class="fas fa-check-circle me-2"></i>🎉 Sistema Saúde Flex Funcionando!</h5>
                    <p class="mb-2">O sistema está rodando corretamente com todas as funcionalidades disponíveis:</p>
                    <ul class="mb-0">
                        <li>✅ Dashboard com estatísticas</li>
                        <li>✅ Gestão de produtos</li>
                        <li>✅ Cadastro de clientes</li>
                        <li>✅ Sistema de vendas</li>
                        <li>✅ Banco de dados funcionando</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
'''

TEMPLATE_PRODUTOS = '''
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Produtos - Saúde Flex</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="fas fa-heartbeat me-2"></i>Saúde Flex
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">
                    <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                </a>
                <a class="nav-link" href="{{ url_for('clientes') }}">
                    <i class="fas fa-users me-1"></i>Clientes
                </a>
                <a class="nav-link" href="{{ url_for('vendas') }}">
                    <i class="fas fa-shopping-cart me-1"></i>Vendas
                </a>
                <span class="navbar-text me-3">
                    <i class="fas fa-user me-1"></i>{{ session.user_nome }}
                </span>
                <a class="nav-link" href="{{ url_for('logout') }}">
                    <i class="fas fa-sign-out-alt me-1"></i>Sair
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <h1 class="h3 mb-4">
                    <i class="fas fa-box me-2 text-primary"></i>
                    Produtos
                </h1>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 fw-bold text-primary">Lista de Produtos</h6>
                    </div>
                    <div class="card-body">
                        {% if produtos %}
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Nome</th>
                                            <th>Descrição</th>
                                            <th>Preço Custo</th>
                                            <th>Preço Venda</th>
                                            <th>Estoque</th>
                                            <th>Margem</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for produto in produtos %}
                                        <tr>
                                            <td><strong>{{ produto.nome }}</strong></td>
                                            <td>{{ produto.descricao or '-' }}</td>
                                            <td>R$ {{ "%.2f"|format(produto.preco_custo) }}</td>
                                            <td><strong>R$ {{ "%.2f"|format(produto.preco_venda) }}</strong></td>
                                            <td>
                                                <span class="badge bg-{{ 'danger' if produto.estoque_atual <= 5 else 'success' }}">
                                                    {{ produto.estoque_atual }}
                                                </span>
                                            </td>
                                            <td>
                                                {% set margem = ((produto.preco_venda - produto.preco_custo) / produto.preco_custo * 100) %}
                                                <span class="badge bg-info">{{ "%.1f"|format(margem) }}%</span>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <div class="text-center py-5">
                                <i class="fas fa-box fa-3x text-muted mb-3"></i>
                                <h5>Nenhum produto cadastrado</h5>
                                <p class="text-muted">Cadastre produtos para começar a usar o sistema.</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
'''

TEMPLATE_CLIENTES = '''
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clientes - Saúde Flex</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="fas fa-heartbeat me-2"></i>Saúde Flex
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">
                    <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                </a>
                <a class="nav-link" href="{{ url_for('produtos') }}">
                    <i class="fas fa-box me-1"></i>Produtos
                </a>
                <a class="nav-link" href="{{ url_for('vendas') }}">
                    <i class="fas fa-shopping-cart me-1"></i>Vendas
                </a>
                <span class="navbar-text me-3">
                    <i class="fas fa-user me-1"></i>{{ session.user_nome }}
                </span>
                <a class="nav-link" href="{{ url_for('logout') }}">
                    <i class="fas fa-sign-out-alt me-1"></i>Sair
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <h1 class="h3 mb-4">
                    <i class="fas fa-users me-2 text-primary"></i>
                    Clientes
                </h1>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 fw-bold text-primary">Lista de Clientes</h6>
                    </div>
                    <div class="card-body">
                        {% if clientes %}
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Nome</th>
                                            <th>Email</th>
                                            <th>Telefone</th>
                                            <th>Cidade</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for cliente in clientes %}
                                        <tr>
                                            <td><strong>{{ cliente.nome }}</strong></td>
                                            <td>{{ cliente.email or '-' }}</td>
                                            <td>{{ cliente.telefone or '-' }}</td>
                                            <td>{{ cliente.cidade or '-' }}</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <div class="text-center py-5">
                                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                <h5>Nenhum cliente cadastrado</h5>
                                <p class="text-muted">Cadastre clientes para começar a usar o sistema.</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
'''

TEMPLATE_VENDAS = '''
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vendas - Saúde Flex</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="fas fa-heartbeat me-2"></i>Saúde Flex
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">
                    <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                </a>
                <a class="nav-link" href="{{ url_for('produtos') }}">
                    <i class="fas fa-box me-1"></i>Produtos
                </a>
                <a class="nav-link" href="{{ url_for('clientes') }}">
                    <i class="fas fa-users me-1"></i>Clientes
                </a>
                <span class="navbar-text me-3">
                    <i class="fas fa-user me-1"></i>{{ session.user_nome }}
                </span>
                <a class="nav-link" href="{{ url_for('logout') }}">
                    <i class="fas fa-sign-out-alt me-1"></i>Sair
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <h1 class="h3 mb-4">
                    <i class="fas fa-shopping-cart me-2 text-primary"></i>
                    Vendas
                </h1>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 fw-bold text-primary">Lista de Vendas</h6>
                    </div>
                    <div class="card-body">
                        {% if vendas %}
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Data</th>
                                            <th>Cliente</th>
                                            <th>Vendedor</th>
                                            <th>Total</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for venda in vendas %}
                                        <tr>
                                            <td><strong>#{{ venda.id }}</strong></td>
                                            <td>{{ venda.data_venda }}</td>
                                            <td>{{ venda.cliente_nome }}</td>
                                            <td>{{ venda.vendedor_nome }}</td>
                                            <td><strong class="text-success">R$ {{ "%.2f"|format(venda.total_final) }}</strong></td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <div class="text-center py-5">
                                <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                                <h5>Nenhuma venda realizada</h5>
                                <p class="text-muted">As vendas aparecerão aqui quando forem realizadas.</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
'''

if __name__ == '__main__':
    print("🚀 Iniciando Saúde Flex...")
    print("📁 Inicializando banco de dados...")

    try:
        init_database()
        print("✅ Banco de dados inicializado")
        print("🌐 Iniciando servidor Flask...")
        print(f"📍 Acesse: http://localhost:{PORT}")
        print("🔑 Login: <EMAIL> / admin123")
        print("-" * 50)

        # Abrir navegador automaticamente
        threading.Thread(target=abrir_navegador, daemon=True).start()

        app.run(debug=False, host='127.0.0.1', port=PORT)

    except Exception as e:
        print(f"❌ Erro ao iniciar: {e}")
        import traceback
        traceback.print_exc()
        input("Pressione Enter para sair...")
